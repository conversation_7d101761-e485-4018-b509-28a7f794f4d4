import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { AuthUser } from '../interfaces/auth-user.interface';
import { JwtPayload } from '../interfaces/jwt-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor() {
    const audience = process.env.Z2DATA_SSO_AUDIENCE || 'api.z2data.com';
    const issuer = process.env.Z2DATA_SSO_AUTHORITY || 'https://testsso.z2data.com';

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true, // Temporarily ignore expiration for debugging
      secretOrKey: 'temporary-secret', // Temporary secret for debugging
      // audience, // Temporarily disable audience validation
      // issuer, // Temporarily disable issuer validation
      algorithms: ['HS256', 'RS256'], // Allow both algorithms temporarily
    });

    this.logger.log(`Initializing JWT Strategy with:`);
    this.logger.log(`Audience: ${audience}`);
    this.logger.log(`Issuer: ${issuer}`);
    this.logger.warn(`TEMPORARY: Using permissive JWT validation for debugging`);
  }

  async validate(payload: any): Promise<AuthUser> {
    this.logger.log(`DEBUGGING: Full JWT payload received:`, JSON.stringify(payload, null, 2));

    // Extract user information from payload with fallbacks
    const userId = payload.sub || payload.id || payload.user_id || 'unknown';
    const email = payload.email || payload.preferred_username || '<EMAIL>';
    const name = payload.name || payload.given_name || payload.preferred_username || 'Unknown User';
    const roles = payload.role || payload.roles || payload.groups || ['FREE'];

    const user: AuthUser = {
      id: userId,
      email: email,
      name: name,
      roles: Array.isArray(roles) ? roles : [roles],
    };

    this.logger.log(`JWT validation successful for user: ${user.email} (ID: ${user.id})`);
    return user;
  }
}
