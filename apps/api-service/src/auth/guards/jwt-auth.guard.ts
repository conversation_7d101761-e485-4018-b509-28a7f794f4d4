import { ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthGuard } from '@nestjs/passport';

import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private reflector: Reflector) {
    super();
  }

  override canActivate(context: ExecutionContext) {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      this.logger.debug('Route is public, skipping authentication');
      return true;
    }

    return super.canActivate(context);
  }

  override getRequest(context: ExecutionContext) {
    // Handle GraphQL context
    if (context.getType<string>() === 'graphql') {
      const ctx = GqlExecutionContext.create(context);
      return ctx.getContext().req;
    }

    // Handle HTTP context
    return context.switchToHttp().getRequest();
  }

  override handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      // Log detailed error information for debugging
      this.logger.error(`Authentication failed:`);
      this.logger.error(`Error: ${err?.message || 'No error'}`);
      this.logger.error(`Info: ${info?.message || 'No info'}`);
      this.logger.error(`User: ${user ? 'Present' : 'Not present'}`);

      if (err) {
        this.logger.error(`Error stack: ${err.stack}`);
      }

      // Extract token for debugging (first 20 chars only for security)
      const request = this.getRequest(context);
      const authHeader = request?.headers?.authorization;
      if (authHeader) {
        const token = authHeader.replace('Bearer ', '');
        this.logger.error(`Token preview: ${token.substring(0, 20)}...`);
      } else {
        this.logger.error('No Authorization header found');
      }

      throw err || new Error('Unauthorized');
    }

    this.logger.debug(`Authentication successful for user: ${user.email}`);
    return user;
  }
}
