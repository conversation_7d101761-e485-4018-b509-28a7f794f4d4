import { z } from "zod";

export const z2PartDataSchema = z.object({
  companyID: z.number(),
  companyName: z.string(),
  groupID: z.number(),
  partID: z.number(),
  partNumber: z.string(),
});

export const z2ValidationResultSchema = z.object({
  manufacture: z.string(),
  matchComment: z.string(),
  matchReason: z.string(),
  matchStatus: z.string(),
  mpn: z.string(),
  rowNumber: z.number(),
  z2PartData: z2PartDataSchema,
});

export const z2ValidationResponseSchema = z.object({
  numFound: z.number(),
  pageNumber: z.number(),
  results: z.array(z2ValidationResultSchema),
  status: z.string(),
  statusCode: z.number(),
});

export const z2ValidationRequestRowSchema = z.object({
  man: z.string(),
  mpn: z.string(),
  rowNumber: z.number(),
});

export const z2ValidationRequestSchema = z.object({
  rows: z.array(z2ValidationRequestRowSchema),
});

export const z2CrossItemSchema = z.object({
  companyName: z.string(),
  crossComment: z.string(),
  crossType: z.string(),
  dataSheet: z.string(),
  package: z.string(),
  partDescription: z.string(),
  partLifecycle: z.string(),
  partNumber: z.string(),
  roHsFlag: z.string().optional(),
});

export const z2CrossDetailsSchema = z.object({
  Total_Crosses_Found: z.number(),
  crosses: z.array(z2CrossItemSchema),
  currentPage: z.number(),
  pageSize: z.number(),
});

export const z2CrossAggregationSchema = z.record(z.string(), z.number());

export const z2CrossResultsSchema = z.object({
  companyName: z.string(),
  crossAggregation: z2CrossAggregationSchema,
  crossesDetails: z2CrossDetailsSchema,
  dataSheet: z.string(),
  numFound: z.number(),
  pageNumber: z.number(),
  partID: z.number(),
  partLifecycle: z.string(),
  partNumber: z.string(),
  roHsFlag: z.string().optional(),
});

export const z2CrossResponseSchema = z.object({
  results: z2CrossResultsSchema,
  status: z.string(),
  statusCode: z.number(),
});

export type Z2PartData = z.infer<typeof z2PartDataSchema>;
export type Z2ValidationResult = z.infer<typeof z2ValidationResultSchema>;
export type Z2ValidationResponse = z.infer<typeof z2ValidationResponseSchema>;
export type Z2ValidationRequestRow = z.infer<
  typeof z2ValidationRequestRowSchema
>;
export type Z2ValidationRequest = z.infer<typeof z2ValidationRequestSchema>;
export type Z2CrossItem = z.infer<typeof z2CrossItemSchema>;
export type Z2CrossDetails = z.infer<typeof z2CrossDetailsSchema>;
export type Z2CrossAggregation = z.infer<typeof z2CrossAggregationSchema>;
export type Z2CrossResults = z.infer<typeof z2CrossResultsSchema>;
export type Z2CrossResponse = z.infer<typeof z2CrossResponseSchema>;

// Part Search Schemas
export const z2PartSearchResultSchema = z.object({
  Datasheet: z.string(),
  Description: z.string(),
  MPN: z.string(),
  Manufacturer: z.string(),
  PartID: z.number(),
  ProductType: z.string(),
});

export const z2PartSearchResponseSchema = z.object({
  results: z.object({
    PartSearch: z.object({
      PageNum: z.number(),
      Result: z.array(z2PartSearchResultSchema),
      Size: z.number(),
      TotalCount: z.number(),
    }),
  }),
  status: z.string(),
  statusCode: z.number(),
});

export type Z2PartSearchResult = z.infer<typeof z2PartSearchResultSchema>;
export type Z2PartSearchResponse = z.infer<typeof z2PartSearchResponseSchema>;

// Part Details by ID Schemas - Simplified for our use case
export const z2MPNSummarySchema = z
  .object({
    PartID: z.number().optional(),
    MPN: z.string().optional(),
    Supplier: z.string().optional(),
    ProductType: z.string().optional(),
    Description: z.string().optional(),
    FamilyName: z.string().optional(),
    LifecycleStatus: z.string().optional(),
    MainCategory: z.string().optional(),
    Category: z.string().optional(),
    SubCategory: z.string().optional(),
  })
  .passthrough();

export const z2LifecycleSchema = z
  .object({
    LifecycleStatus: z.string().optional(),
    EstimatedYearsToEOL: z.number().optional(),
    ForecastedObsolescenceYear: z.number().optional(),
  })
  .passthrough();

export const z2ComplianceDetailsSchema = z
  .object({
    RoHSStatus: z.string().optional(),
    RoHSVersion: z.string().optional(),
    REACHStatus: z.string().optional(),
    LeadFree: z.boolean().optional(),
  })
  .passthrough();

export const z2MarketAvailabilitySummarySchema = z
  .object({
    MarketStatus: z.string().optional(),
    NumberOfSeller: z.number().optional(),
    TotalQuantityAvailable: z.number().optional(),
    LowestPrice: z.number().optional(),
    MinLeadTime_weeks: z.number().optional(),
    MaxLeadTime_weeks: z.number().optional(),
  })
  .passthrough();

export const z2PartDetailsResultsSchema = z
  .object({
    MPNSummary: z2MPNSummarySchema.optional(),
    Lifecycle: z2LifecycleSchema.optional(),
    ComplianceDetails: z2ComplianceDetailsSchema.optional(),
    MarketAvailabilitySummary: z2MarketAvailabilitySummarySchema.optional(),
  })
  .passthrough();

export const z2PartDetailsResponseSchema = z.object({
  statusCode: z.number(),
  status: z.string(),
  results: z2PartDetailsResultsSchema,
});

export type Z2MPNSummary = z.infer<typeof z2MPNSummarySchema>;
export type Z2Lifecycle = z.infer<typeof z2LifecycleSchema>;
export type Z2ComplianceDetails = z.infer<typeof z2ComplianceDetailsSchema>;
export type Z2MarketAvailabilitySummary = z.infer<typeof z2MarketAvailabilitySummarySchema>;
export type Z2PartDetailsResults = z.infer<typeof z2PartDetailsResultsSchema>;
export type Z2PartDetailsResponse = z.infer<typeof z2PartDetailsResponseSchema>;
