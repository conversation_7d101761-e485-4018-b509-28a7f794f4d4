import { Injectable, Logger } from "@nestjs/common";

import { PrismaService } from "../prisma.service";
import { IMetaData } from "./metadata.model";

@Injectable()
export class MetadataService {
  private readonly logger = new Logger(MetadataService.name);

  constructor(private readonly prisma: PrismaService) {}

  async getUserMetaData(userId: string, key: string) {
    try {
      const metaData = await this.prisma.user_meta_data.findFirst({
        where: {
          key,
          user_id: userId,
        },
      });
      return metaData?.value;
    } catch (error) {
      this.logger.error(
        `Failed to get user meta data: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  async setUserMetaData(userId: string, key: string, value: IMetaData) {
    try {
      // Use upsert for better performance and atomicity
      return await this.prisma.user_meta_data.upsert({
        where: {
          key_user_id: {
            key,
            user_id: userId,
          },
        },
        update: {
          value,
        },
        create: {
          key,
          user_id: userId,
          value,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to set user meta data: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /*
   * Legacy method for backward compatibility with number user IDs
   * This will be removed once all services are migrated
   */
  async getUserMetaDataLegacy(userId: number, key: string) {
    this.logger.warn(`Using legacy getUserMetaData with number userId: ${userId}. Please migrate to string-based user IDs.`);
    return this.getUserMetaData(userId.toString(), key);
  }

  async setUserMetaDataLegacy(userId: number, key: string, value: IMetaData) {
    this.logger.warn(`Using legacy setUserMetaData with number userId: ${userId}. Please migrate to string-based user IDs.`);
    return this.setUserMetaData(userId.toString(), key, value);
  }
}
