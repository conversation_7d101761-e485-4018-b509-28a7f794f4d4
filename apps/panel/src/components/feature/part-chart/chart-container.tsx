"use client";

import { useState } from "react";

import { ChartDataState as ComponentChartDataState } from "@/components/primitives/chart/chart.type";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";

import { useChartContainer } from "./chart.hook";
import { ChartContainerProps } from "./chart.type";
import { ChartResults } from "./chart-results";

function ChartSkeleton({
  title,
  subtitle,
}: {
  title: string;
  subtitle: string;
}) {
  return (
    <div className="bg-white rounded-lg border p-6 animate-pulse">
      <div className="mb-4">
        <div className="h-5 bg-gray-200 rounded w-32 mb-2"></div>
        <div className="h-4 bg-gray-100 rounded w-24"></div>
      </div>
      <div className="bg-gray-100 rounded" style={{ height: "460px" }}></div>
    </div>
  );
}

function LoadingSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <ChartSkeleton
        title="Stock Level Trend"
        subtitle="Stock Levels Over Time"
      />
      <ChartSkeleton title="Lead Time Trend" subtitle="Lead Time Over Time" />
      <ChartSkeleton
        title="Average Price Trend"
        subtitle="Average Price Over Time"
      />
      <ChartSkeleton
        title="Average Price Per Distributor"
        subtitle="Average Price Per Distributor Over Time"
      />
    </div>
  );
}

const transformChartData = (
  data: Record<string, { week: string; value: number | null }[]> | null,
): ComponentChartDataState => {
  if (!data) return null;

  return Object.entries(data).reduce(
    (acc, [key, items]) => {
      acc[key] = {
        items: items.map((item) => ({ value: item.value, week: item.week })),
        link: "",
      };
      return acc;
    },
    {} as Record<
      string,
      { items: { week: string; value: number | null }[]; link: string }
    >,
  );
};

export function ChartContainer({
  partId,
  enabled,
  priceBreak,
  breakMethod,
  renderAsDrawer = false,
  drawerTrigger,
  drawerTitle,
  drawerDescription,
  disableAI,
}: ChartContainerProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Only fetch data when not rendering as drawer or when drawer is open
  const shouldFetchData = renderAsDrawer ? isDrawerOpen && enabled : enabled;

  const {
    isLoading,
    error,
    priceData,
    averagePriceData,
    stockData,
    leadTimeData,
  } = useChartContainer({
    breakMethod,
    enabled: shouldFetchData,
    partId,
    priceBreak,
  });

  const chartResultsComponent = isLoading ? (
    <LoadingSkeleton />
  ) : (
    <ChartResults
      priceData={transformChartData(priceData)}
      averagePriceData={transformChartData(averagePriceData)}
      stockData={transformChartData(stockData)}
      leadTimeData={transformChartData(leadTimeData)}
      isLoading={isLoading}
      error={error}
      enabled={shouldFetchData}
      disableAI={disableAI}
    />
  );

  if (!renderAsDrawer) {
    return chartResultsComponent;
  }

  return (
    <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
      {drawerTrigger && <DrawerTrigger asChild>{drawerTrigger}</DrawerTrigger>}
      <DrawerContent className=" pb-48">
        {(drawerTitle || drawerDescription) && (
          <DrawerHeader className="px-6 py-6">
            {drawerTitle && <DrawerTitle>{drawerTitle}</DrawerTitle>}
            {drawerDescription && (
              <DrawerDescription>{drawerDescription}</DrawerDescription>
            )}
          </DrawerHeader>
        )}
        <ScrollArea className="h-[85vh] px-6 pb-48">
          {chartResultsComponent}
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
}
