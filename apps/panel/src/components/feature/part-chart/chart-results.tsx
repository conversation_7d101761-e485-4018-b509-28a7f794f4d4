"use client";

import { Loader } from "lucide-react";

import { AiAnalysisComponent } from "@/components/primitives/ai-analysis/ai-analysis";
import { Chart } from "@/components/primitives/chart/chart";
import {
  ChartDataState,
  ChartTypes,
} from "@/components/primitives/chart/chart.type";

interface ChartResultsProps {
  priceData?: ChartDataState;
  averagePriceData?: ChartDataState;
  stockData: ChartDataState;
  leadTimeData: ChartDataState;
  isLoading: boolean;
  error: Error | null;
  enabled: boolean;
  disableAI?: boolean;
}

export function ChartResults({
  priceData,
  averagePriceData,
  stockData,
  leadTimeData,
  isLoading,
  error,
  enabled,
  disableAI,
}: ChartResultsProps) {
  if (isLoading) {
    return (
      <div className="relative h-60">
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 z-10 rounded-xl">
          <Loader className="h-8 w-8 text-blue-600 animate-spin" />
        </div>
      </div>
    );
  }

  if (!enabled) {
    return (
      <div className="text-center text-gray-500 py-10">
        Please click the Filter button to see results.
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 py-10">
        Error loading data: {error.message}
      </div>
    );
  }

  const hasData = priceData || averagePriceData || stockData || leadTimeData;

  if (!hasData) {
    return (
      <div className="text-center text-gray-500 py-10">
        No data available for the selected filters.
      </div>
    );
  }

  const allChartsData = {
    averagePrice: averagePriceData,
    leadTime: leadTimeData,
    pricePerDistributor: priceData,
    stock: stockData,
  };

  return (
    <div className="space-y-6 transition-all duration-300">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {stockData && (
          <Chart
            type={ChartTypes.stock}
            data={stockData}
            showDecimals={false}
          />
        )}
        {leadTimeData && (
          <Chart
            type={ChartTypes.lead_time}
            data={leadTimeData}
            showDecimals={false}
          />
        )}
        {averagePriceData && (
          <Chart type={ChartTypes.price} data={averagePriceData} />
        )}
        {priceData && (
          <Chart type={ChartTypes.price_per_supplier} data={priceData} />
        )}
      </div>
      {!disableAI && (
        <div className="mt-8">
          <AiAnalysisComponent
            question="You are a market analyst specializing in electronic components. Based on the historical input data provided, generate an insightful report for a procurement professional.
Instructions:
- The input includes:
  - Overall average price trend
  - Average price trend per distributor
  - Total stock level trend
 
  - Average lead time trend
- Focus the analysis on the last 1–3 months while considering longer-term context if relevant.
- Highlight any risks or anomalies (e.g., price spikes, stock depletion, lead time surges).
- Provide actionable recommendations for the procurement team (e.g., buy ahead, switch suppliers, monitor trends).
- Maintain an analytical and professional tone throughout the report, use numeric metrics.
Output:
Generate a concise summary table:
headers: metric, summary, takeaway, actionable recommendation
it has three rows to describe.
fill the first column with:
- price
- stock
- lead time
only response the table, no other text.
"
            dataPayload={allChartsData}
            title="AI Market Analyst"
          />
        </div>
      )}
    </div>
  );
}
