export type InputDataItem = {
  week: string;
  value: number | null;
};

export type ChartDataState = Record<string, InputDataItem[]> | null;

export interface ChartContainerProps {
  partId?: string;
  enabled: boolean;
  priceBreak?: string;
  breakMethod?: "min" | "avg";
  // Drawer properties
  renderAsDrawer?: boolean;
  drawerTrigger?: React.ReactNode;
  drawerTitle?: string;
  drawerDescription?: string;
  disableAI?: boolean;
}
