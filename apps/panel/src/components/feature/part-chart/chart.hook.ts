import { useEffect, useState } from "react";

import { fetchWeeklyTrend } from "@/lib/mi/apis/weekly-trend";
import { TrendData } from "@/lib/mi/schemas";

import {
  ChartContainerProps,
  ChartDataState,
  InputDataItem,
} from "./chart.type";

export function useChartContainer({
  partId,
  enabled,
  priceBreak,
  breakMethod,
}: ChartContainerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [priceData, setPriceData] = useState<ChartDataState>(null);
  const [averagePriceData, setAveragePriceData] =
    useState<ChartDataState>(null);
  const [stockData, setStockData] = useState<ChartDataState>(null);
  const [leadTimeData, setLeadTimeData] = useState<ChartDataState>(null);

  useEffect(() => {
    if (enabled && partId) {
      fetchChartData();
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enabled, partId, priceBreak, breakMethod]);

  const fetchChartData = async () => {
    if (!partId) return;

    setIsLoading(true);
    setPriceData(null);
    setAveragePriceData(null);
    setStockData(null);
    setLeadTimeData(null);
    setError(null);

    try {
      const averagePriceTrend = async () => {
        const priceResult = await fetchWeeklyTrend({
          partId,
          targetAttribute: `price_${priceBreak || 1000}`,
          targetType: breakMethod,
          withSeller: true,
        });

        return priceResult.reduce<Record<string, InputDataItem[]>>(
          (acc, curr: TrendData) => {
            const sellerId = String(curr.seller_name ?? "General");
            if (!acc[sellerId]) {
              acc[sellerId] = [];
            }
            acc[sellerId].push({
              value: curr.value,
              week: curr.week,
            });
            return acc;
          },
          {},
        );
      };

      const averagePricePerDistributor = async () => {
        const averagePriceResult = await fetchWeeklyTrend({
          partId,
          targetAttribute: `price_${priceBreak || 1000}`,
          targetType: breakMethod,
          withSeller: false,
        });

        return averagePriceResult.reduce<Record<string, InputDataItem[]>>(
          (acc, curr: TrendData) => {
            const key = "Average Price";
            if (!acc[key]) {
              acc[key] = [];
            }
            acc[key].push({
              value: curr.value,
              week: curr.week,
            });
            return acc;
          },
          {},
        );
      };

      const stockLevelTrend = async () => {
        const stockResult = await fetchWeeklyTrend({
          partId,
          targetAttribute: "stock",
        });

        return stockResult.reduce<Record<string, InputDataItem[]>>(
          (acc, curr: TrendData) => {
            const key = "Stock Levels";
            if (!acc[key]) {
              acc[key] = [];
            }
            acc[key].push({
              value: curr.value,
              week: curr.week,
            });
            return acc;
          },
          {},
        );
      };

      const leadtimeTrend = async () => {
        const leadTimeResult = await fetchWeeklyTrend({
          partId,
          targetAttribute: "lead_time",
          targetType: breakMethod,
        });

        return leadTimeResult.reduce<Record<string, InputDataItem[]>>(
          (acc, curr: TrendData) => {
            const key = "Lead Time";
            if (!acc[key]) {
              acc[key] = [];
            }
            acc[key].push({
              value: curr.value,
              week: curr.week,
            });
            return acc;
          },
          {},
        );
      };

      const groupedPriceData = await averagePriceTrend();
      const groupedAveragePriceData = await averagePricePerDistributor();
      const groupedStockData = await stockLevelTrend();
      const groupedLeadTimeData = await leadtimeTrend();
      setPriceData(groupedPriceData);
      setAveragePriceData(groupedAveragePriceData);
      setStockData(groupedStockData);
      setLeadTimeData(groupedLeadTimeData);
    } catch (err) {
      console.error("Error fetching chart data:", err);
      setError(
        err instanceof Error ? err : new Error("An unknown error occurred"),
      );
    } finally {
      setIsLoading(false);
    }
  };

  return {
    averagePriceData,
    error,
    isLoading,
    leadTimeData,
    priceData,
    stockData,
  };
}
