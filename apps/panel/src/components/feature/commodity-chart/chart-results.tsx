"use client";

import { Loader } from "lucide-react";

import { Chart } from "@/components/primitives/chart/chart";
import {
  ChartDataState,
  ChartTypes,
} from "@/components/primitives/chart/chart.type";

interface ChartResultsProps {
  commodityAveragePriceData?: ChartDataState;
  stockData: ChartDataState;
  leadTimeData: ChartDataState;
  isLoading: boolean;
  error: Error | null;
  enabled: boolean;
}

export function ChartResults({
  commodityAveragePriceData,
  stockData,
  leadTimeData,
  isLoading,
  error,
  enabled,
}: ChartResultsProps) {
  if (isLoading) {
    return (
      <div className="relative h-60">
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 z-10 rounded-xl">
          <Loader className="h-8 w-8 text-blue-600 animate-spin" />
        </div>
      </div>
    );
  }

  if (!enabled) {
    return (
      <div className="text-center text-gray-500 py-10">
        Please click the Filter button to see results.
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 py-10">
        Error loading data: {error.message}
      </div>
    );
  }

  const hasData = stockData || leadTimeData || commodityAveragePriceData;

  if (!hasData) {
    return (
      <div className="text-center text-gray-500 py-10">
        No data available for the selected filters.
      </div>
    );
  }

  return (
    <div className="grid gap-6 transition-all duration-300">
      <div className="grid grid-cols-1 gap-6">
        {commodityAveragePriceData && (
          <div className="space-y-4">
            <Chart
              type={ChartTypes.commodity_average_price}
              data={commodityAveragePriceData}
            />
          </div>
        )}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {stockData && (
          <div className="space-y-4">
            <Chart
              type={ChartTypes.stock}
              data={stockData}
              showDecimals={false}
            />
          </div>
        )}
        {leadTimeData && (
          <div className="space-y-4">
            <Chart
              type={ChartTypes.lead_time}
              data={leadTimeData}
              showDecimals={false}
            />
          </div>
        )}
      </div>
    </div>
  );
}
