import { useEffect, useState } from "react";

import {
  ChartData,
  ChartDataState,
} from "@/components/primitives/chart/chart.type";
import { fetchGrades, fetchManufacturers, fetchPackaging } from "@/lib/mi/api";
import { fetchWeeklyTrend } from "@/lib/mi/apis/weekly-trend";
import { TrendData } from "@/lib/mi/schemas";

export interface FilterSet {
  id: string;
  manufacturerId?: string;
  gradeId?: string;
  packagingId?: string;
}

type BaseFilters = {
  commodityId?: string;
  subCommodityId?: string;
  subSubCommodityId?: string;
  priceBreak?: string;
  breakMethod?: string;
  productLineId?: string;
};

interface FetchChartDataProps {
  filterSets: FilterSet[];
  baseFilters: BaseFilters;
  enabled: boolean;
}

export function useFetchChartData({
  filterSets,
  baseFilters,
  enabled,
}: FetchChartDataProps) {
  const [priceData, setPriceData] = useState<ChartDataState>(null);
  const [stockData, setStockData] = useState<ChartDataState>(null);
  const [leadTimeData, setLeadTimeData] = useState<ChartDataState>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  useEffect(() => {
    if (!enabled || !baseFilters.commodityId || filterSets.length === 0) {
      return;
    }

    let isMounted = true;
    setIsLoading(true);
    setPriceData(null);
    setStockData(null);
    setLeadTimeData(null);
    setError(null);

    const setNameCache = new Map<string, Promise<string>>();

    const getSetName = (
      filterSet: FilterSet,
      index: number,
    ): Promise<string> => {
      const cacheKey = JSON.stringify(filterSet);
      if (!setNameCache.has(cacheKey)) {
        setNameCache.set(cacheKey, generateSetName(filterSet, index + 1));
      }
      return setNameCache.get(cacheKey)!;
    };

    const fetchDataForAttribute = async (targetAttribute: string) => {
      const allDataBySet: Record<string, ChartData> = {};

      try {
        const fetchPromises = filterSets.map(async (filterSet, index) => {
          try {
            const setName = await getSetName(filterSet, index);
            const link = buildLink(filterSet, baseFilters);
            const data = await fetchWeeklyTrend({
              commodityId: baseFilters.commodityId,
              gradeId: filterSet.gradeId,
              manufacturerId: filterSet.manufacturerId,
              packagingId: filterSet.packagingId,
              plnameId: baseFilters.productLineId,
              subcommodityId: baseFilters.subCommodityId,
              subsubcommodityId: baseFilters.subSubCommodityId,
              targetAttribute,
              targetType: getTargetType(
                targetAttribute,
                baseFilters.breakMethod,
              ),
            });

            allDataBySet[setName] = {
              items: data.map((item: TrendData) => ({
                ...item,
              })),
              link,
            };
          } catch (err) {
            console.error(`Error fetching data for set ${index + 1}:`, err);
          }
        });

        await Promise.all(fetchPromises);

        return Object.keys(allDataBySet).length > 0 ? allDataBySet : null;
      } catch (err) {
        console.error(`Error fetching ${targetAttribute} data:`, err);
        throw err;
      }
    };

    const fetchAllData = async () => {
      try {
        const [fetchedPriceData, fetchedStockData, fetchedLeadTimeData] =
          await Promise.all([
            fetchDataForAttribute(`price_${baseFilters.priceBreak || "1000"}`),
            fetchDataForAttribute("stock"),
            fetchDataForAttribute("lead_time"),
          ]);

        if (isMounted) {
          setPriceData(fetchedPriceData);
          setStockData(fetchedStockData);
          setLeadTimeData(fetchedLeadTimeData);
          setIsLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setError(
            err instanceof Error ? err : new Error("An unknown error occurred"),
          );
          setIsLoading(false);
        }
      }
    };

    fetchAllData();

    return () => {
      isMounted = false;
    };
  }, [filterSets, baseFilters, enabled]);

  return { error, isLoading, leadTimeData, priceData, stockData };
}

async function generateSetName(
  filterSet: FilterSet,
  index: number,
): Promise<string> {
  const fetchPromises = [];
  const nameMap: Record<string, string> = {};

  if (filterSet.manufacturerId) {
    fetchPromises.push(
      fetchManufacturers({ id: Number(filterSet.manufacturerId) })
        .then((data) => {
          nameMap.manufacturer =
            data?.[0]?.name || `Mfr ${filterSet.manufacturerId}`;
        })
        .catch(() => {
          nameMap.manufacturer = `Mfr ${filterSet.manufacturerId}`;
        }),
    );
  }

  if (filterSet.gradeId) {
    fetchPromises.push(
      fetchGrades({ id: Number(filterSet.gradeId) })
        .then((data) => {
          nameMap.grade = data?.[0]?.name || `Grade ${filterSet.gradeId}`;
        })
        .catch(() => {
          nameMap.grade = `Grade ${filterSet.gradeId}`;
        }),
    );
  }

  if (filterSet.packagingId) {
    fetchPromises.push(
      fetchPackaging({ id: Number(filterSet.packagingId) })
        .then((data) => {
          nameMap.packaging =
            data?.[0]?.name || `Packaging ${filterSet.packagingId}`;
        })
        .catch(() => {
          nameMap.packaging = `Packaging ${filterSet.packagingId}`;
        }),
    );
  }

  await Promise.all(fetchPromises);

  const parts = [];
  if (nameMap.manufacturer) parts.push(`Mfr: "${nameMap.manufacturer}"`);
  if (nameMap.grade) parts.push(`Grade: "${nameMap.grade}"`);
  if (nameMap.packaging) parts.push(`Packaging: "${nameMap.packaging}"`);

  const formatter = new Intl.ListFormat(undefined, {
    style: "long",
    type: "conjunction",
  });

  return parts.length > 0
    ? `Set ${index}: ${formatter.format(parts)}`
    : `Set ${index}`;
}

function buildLink(filterSet: FilterSet, baseFilters: BaseFilters) {
  const params = new URLSearchParams();
  if (baseFilters.commodityId)
    params.set("commodityId", baseFilters.commodityId);
  if (baseFilters.subCommodityId)
    params.set("subCommodityId", baseFilters.subCommodityId);
  if (baseFilters.subSubCommodityId)
    params.set("subSubCommodityId", baseFilters.subSubCommodityId);
  if (baseFilters.productLineId)
    params.set("productLineId", baseFilters.productLineId);

  if (filterSet.manufacturerId)
    params.set("manufacturerId", filterSet.manufacturerId);
  if (filterSet.gradeId) params.set("gradeId", filterSet.gradeId);
  if (filterSet.packagingId) params.set("packagingId", filterSet.packagingId);
  if (baseFilters.priceBreak) params.set("priceBreak", baseFilters.priceBreak);
  if (baseFilters.breakMethod)
    params.set("breakMethod", baseFilters.breakMethod);

  return `trend/parts?${params.toString()}`;
}
function getTargetType(
  targetAttribute: string,
  breakMethod: string | undefined,
): string | undefined {
  if (targetAttribute === "lead_time") {
    return "avg";
  }
  return breakMethod;
}
