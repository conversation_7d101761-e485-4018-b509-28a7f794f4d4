"use client";

import { ChartResults } from "./chart-results";

import type { FilterSet } from "./chart-container.hook";
import { useFetchChartData } from "./chart-container.hook";

interface ChartContainerProps {
  filterSets: FilterSet[];
  baseFilters: {
    commodityId?: string;
    subCommodityId?: string;
    subSubCommodityId?: string;
    productLineId?: string;
    priceBreak?: string;
  };
  enabled: boolean;
}

export function ChartContainer({
  filterSets,
  baseFilters,
  enabled,
}: ChartContainerProps) {
  const { priceData, stockData, leadTimeData, isLoading, error } =
    useFetchChartData({
      baseFilters,
      enabled,
      filterSets,
    });

  return (
    <ChartResults
      commodityAveragePriceData={priceData}
      stockData={stockData}
      leadTimeData={leadTimeData}
      isLoading={isLoading}
      error={error}
      enabled={enabled}
    />
  );
}
