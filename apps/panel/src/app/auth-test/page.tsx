"use client";

import { useQuery } from "@apollo/client";
import { useEffect, useState } from "react";

import { useAuth } from "@/hooks/use-auth.hook";
import { getAccessToken } from "@/lib/auth-oidc";
import { getAuthHeaders } from "@/lib/auth-oidc.utils";
import { GET_QUOTES } from "@/queries/quotes";

export default function AuthTestPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [authHeaders, setAuthHeaders] = useState<Record<string, string>>({});
  const [accessToken, setAccessToken] = useState<string | null>(null);

  // Test GraphQL query to see if auth headers are included
  const { loading, error, data } = useQuery(GET_QUOTES, {
    errorPolicy: 'all', // Show errors but don't crash
  });

  useEffect(() => {
    const loadAuthInfo = async () => {
      try {
        const headers = await getAuthHeaders();
        const token = await getAccessToken();
        setAuthHeaders(headers);
        setAccessToken(token);
      } catch (error) {
        console.error("Failed to load auth info:", error);
      }
    };

    loadAuthInfo();
  }, [isAuthenticated]);

  if (isLoading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>
        <p>Loading authentication status...</p>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication Test Page</h1>
      
      {/* Authentication Status */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Authentication Status</h2>
        <div className="space-y-2">
          <p>
            <span className="font-medium">Authenticated:</span>{" "}
            <span className={isAuthenticated ? "text-green-600" : "text-red-600"}>
              {isAuthenticated ? "Yes" : "No"}
            </span>
          </p>
          {user && (
            <>
              <p><span className="font-medium">User ID:</span> {user.id}</p>
              <p><span className="font-medium">Email:</span> {user.email}</p>
              <p><span className="font-medium">Name:</span> {user.name}</p>
              <p><span className="font-medium">Roles:</span> {user.role?.join(", ")}</p>
            </>
          )}
        </div>
      </div>

      {/* Auth Headers */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Auth Headers</h2>
        <div className="space-y-2">
          <p>
            <span className="font-medium">Has Authorization Header:</span>{" "}
            <span className={authHeaders.Authorization ? "text-green-600" : "text-red-600"}>
              {authHeaders.Authorization ? "Yes" : "No"}
            </span>
          </p>
          {authHeaders.Authorization && (
            <p className="text-sm text-gray-600 break-all">
              <span className="font-medium">Authorization:</span> {authHeaders.Authorization.substring(0, 50)}...
            </p>
          )}
          <p>
            <span className="font-medium">Access Token Length:</span>{" "}
            {accessToken ? accessToken.length : 0} characters
          </p>
        </div>
      </div>

      {/* GraphQL Query Test */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">GraphQL Query Test</h2>
        <p className="text-sm text-gray-600 mb-3">
          Testing GET_QUOTES query to see if authentication headers are included
        </p>
        
        {loading && <p className="text-blue-600">Loading quotes...</p>}
        
        {error && (
          <div className="text-red-600">
            <p className="font-medium">GraphQL Error:</p>
            <p className="text-sm">{error.message}</p>
            {error.networkError && (
              <p className="text-sm">Network Error: {error.networkError.message}</p>
            )}
          </div>
        )}
        
        {data && (
          <div className="text-green-600">
            <p className="font-medium">Success!</p>
            <p className="text-sm">
              Received {data.getDigiKeyQuotes?.TotalQuotes || 0} quotes
            </p>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Test Instructions</h2>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>If not authenticated, you should see "No" for authentication status</li>
          <li>Click the login button in the navigation to authenticate</li>
          <li>After authentication, refresh this page</li>
          <li>You should see "Yes" for authentication status and auth headers</li>
          <li>The GraphQL query should either succeed or show a meaningful error</li>
          <li>Check the browser console for Apollo Auth logs</li>
        </ol>
      </div>
    </div>
  );
}
