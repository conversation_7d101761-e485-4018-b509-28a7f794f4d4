import { useEffect, useState } from "react";

import { Supply } from "@/app/part/_components";

import { FilterState } from "./filter-panel";

interface UseFilterPanelProps {
  supplies: Supply[];
  onFilterChange: (filters: FilterState) => void;
}

export function useFilterPanel({
  supplies,
  onFilterChange,
}: UseFilterPanelProps) {
  const [filters, setFilters] = useState<FilterState>({
    authorizationStatus: new Set<string>(),
    leadTimes: new Set<string>(),
    manufacturers: new Set<string>(),
    packagings: new Set<string>(),
    stockRange: new Set<string>(),
    suppliers: new Set<string>(),
  });

  // Create a map of lowercase -> original case for deduplication of manufacturers
  const manufacturerMap = new Map<string, string>();
  supplies.forEach((supply) => {
    if (supply.manufacturer) {
      const lowerCaseName = supply.manufacturer.toLowerCase();
      // Only set if not already in map, to preserve first occurrence
      if (!manufacturerMap.has(lowerCaseName)) {
        manufacturerMap.set(lowerCaseName, supply.manufacturer);
      }
    }
  });

  const uniqueManufacturers = Array.from(manufacturerMap.values()).sort();

  // Create a map of lowercase -> original case for deduplication of suppliers
  const supplierMap = new Map<string, string>();
  supplies.forEach((supply) => {
    if (supply.supplier) {
      const lowerCaseName = supply.supplier.toLowerCase();
      if (!supplierMap.has(lowerCaseName)) {
        supplierMap.set(lowerCaseName, supply.supplier);
      }
    }
  });

  const uniqueSuppliers = Array.from(supplierMap.values()).sort();

  // Create a map of lowercase -> original case for deduplication of packagings
  const packagingMap = new Map<string, string>();
  supplies.forEach((supply) => {
    if (supply.packaging) {
      const lowerCaseName = supply.packaging.toLowerCase();
      if (!packagingMap.has(lowerCaseName)) {
        packagingMap.set(lowerCaseName, supply.packaging);
      }
    }
  });

  const uniquePackagings = Array.from(packagingMap.values()).sort();

  // Create a map of lowercase -> original case for deduplication of leadTimes
  const leadTimeMap = new Map<string, string>();
  supplies.forEach((supply) => {
    if (supply.leadTime) {
      const lowerCaseName = supply.leadTime.toLowerCase();
      if (!leadTimeMap.has(lowerCaseName)) {
        leadTimeMap.set(lowerCaseName, supply.leadTime);
      }
    }
  });

  const uniqueLeadTimes = Array.from(leadTimeMap.values()).sort();

  const handleFilterChange = (
    category: keyof Omit<FilterState, "stockRange" | "authorizationStatus">,
    value: string,
    checked: boolean,
  ) => {
    setFilters((prevFilters) => {
      const newFilters = { ...prevFilters };
      const set = new Set(prevFilters[category]);

      if (checked) {
        set.add(value);
      } else {
        set.delete(value);
      }

      newFilters[category] = set;
      return newFilters;
    });
  };

  const handleStockRangeChange = (value: string, checked: boolean) => {
    setFilters((prevFilters) => {
      const newStockRange = new Set(prevFilters.stockRange);

      if (checked) {
        newStockRange.add(value);
      } else {
        newStockRange.delete(value);
      }

      return {
        ...prevFilters,
        stockRange: newStockRange,
      };
    });
  };

  const handleAuthorizationStatusChange = (value: string, checked: boolean) => {
    setFilters((prevFilters) => {
      const newAuthorizationStatus = new Set(prevFilters.authorizationStatus);

      if (checked) {
        newAuthorizationStatus.add(value);
      } else {
        newAuthorizationStatus.delete(value);
      }

      return {
        ...prevFilters,
        authorizationStatus: newAuthorizationStatus,
      };
    });
  };

  const handleLeadTimeChange = (value: string, checked: boolean) => {
    handleFilterChange("leadTimes", value, checked);
  };

  useEffect(() => {
    onFilterChange(filters);
  }, [filters, onFilterChange]);

  return {
    filters,
    handleAuthorizationStatusChange,
    handleFilterChange,
    handleLeadTimeChange,
    handleStockRangeChange,
    uniqueLeadTimes,
    uniqueManufacturers,
    uniquePackagings,
    uniqueSuppliers,
  };
}
