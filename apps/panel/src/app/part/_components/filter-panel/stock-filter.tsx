import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { STOCK_RANGES } from "./filter-panel.const";

interface StockFilterProps {
  selectedRanges: Set<string>;
  onChange: (value: string, checked: boolean) => void;
}

export function StockFilter({ selectedRanges, onChange }: StockFilterProps) {
  return (
    <div className="space-y-2">
      {STOCK_RANGES.map((range) => (
        <div key={range.id} className="flex items-center space-x-2">
          <Checkbox
            id={`stock-${range.id}`}
            checked={selectedRanges.has(range.id)}
            onCheckedChange={(checked: boolean | "indeterminate") =>
              onChange(range.id, checked === true)
            }
          />
          <Label
            htmlFor={`stock-${range.id}`}
            className="text-sm text-gray-700 cursor-pointer"
          >
            {range.label}
          </Label>
        </div>
      ))}
    </div>
  );
}
