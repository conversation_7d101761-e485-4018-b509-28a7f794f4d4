export const STOCK_RANGES = [
  { id: "1-1000", label: "1 to 1000", max: 1000, min: 1 },
  { id: "1001-10000", label: "1001 to 10000", max: 10000, min: 1001 },
  { id: "10001-100000", label: "10001 to 100000", max: 100000, min: 10001 },
  { id: "in-stock", label: "In Stock Only", max: Infinity, min: 1 },
];

export const STOCK_RANGES_MAP: Record<string, { min: number; max: number }> = {
  "1-1000": { max: 1000, min: 1 },
  "10001-100000": { max: 100000, min: 10001 },
  "1001-10000": { max: 10000, min: 1001 },
  "in-stock": { max: Infinity, min: 1 },
};

export const AUTHORIZATION_STATUS = [
  { id: "authorized", label: "Authorized" },
  { id: "non-authorized", label: "Non-authorized" },
];
