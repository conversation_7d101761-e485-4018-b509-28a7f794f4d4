import { Product, Supply } from "@/app/part/_components";

import { FilterState } from "./filter-panel";
import { STOCK_RANGES_MAP } from "./filter-panel.const";

export function applyFiltersToProduct(
  product: Product,
  filters: FilterState,
): Product {
  const filteredSupplies = applyFiltersToSupplies(product.supplies, filters);

  return {
    ...product,
    supplies: filteredSupplies,
  };
}

export function applyFiltersToSupplies(
  supplies: Supply[],
  filters: FilterState,
): Supply[] {
  // If no filters are active, return all supplies
  if (
    filters.manufacturers.size === 0 &&
    filters.suppliers.size === 0 &&
    filters.packagings.size === 0 &&
    filters.stockRange.size === 0 &&
    filters.authorizationStatus.size === 0 &&
    (filters.leadTimes?.size === 0 || filters.leadTimes === undefined)
  ) {
    return supplies;
  }

  return supplies.filter((supply) => {
    // Filter by manufacturer - case insensitive comparison
    if (filters.manufacturers.size > 0) {
      const manufacturerMatches = Array.from(filters.manufacturers).some(
        (filterManufacturer) =>
          supply.manufacturer.toLowerCase() ===
          filterManufacturer.toLowerCase(),
      );

      if (!manufacturerMatches) {
        return false;
      }
    }

    // Filter by supplier - case insensitive comparison
    if (filters.suppliers.size > 0) {
      const supplierMatches = Array.from(filters.suppliers).some(
        (filterSupplier) =>
          supply.supplier.toLowerCase() === filterSupplier.toLowerCase(),
      );

      if (!supplierMatches) {
        return false;
      }
    }

    // Filter by packaging - case insensitive comparison
    if (filters.packagings.size > 0 && supply.packaging) {
      const packagingMatches = Array.from(filters.packagings).some(
        (filterPackaging) =>
          supply.packaging?.toLowerCase() === filterPackaging.toLowerCase(),
      );

      if (!packagingMatches) {
        return false;
      }
    } else if (filters.packagings.size > 0 && !supply.packaging) {
      return false;
    }

    // Filter by stock range
    if (filters.stockRange.size > 0) {
      const supplyStock = supply.stock || 0;

      // Check if supply stock falls within any of the selected ranges
      const matchesAnyRange = Array.from(filters.stockRange).some((rangeId) => {
        const range = STOCK_RANGES_MAP[rangeId];
        if (!range) return false;

        return supplyStock >= range.min && supplyStock <= range.max;
      });

      if (!matchesAnyRange) {
        return false;
      }
    }

    // Filter by authorization status - now using the authorized field from the API
    if (filters.authorizationStatus.size > 0) {
      const isAuthorized = Boolean(supply.authorized);

      if (
        (filters.authorizationStatus.has("authorized") && !isAuthorized) ||
        (filters.authorizationStatus.has("non-authorized") && isAuthorized)
      ) {
        return false;
      }
    }

    // Filter by lead time - case insensitive comparison
    if (filters.leadTimes?.size > 0 && supply.leadTime) {
      const leadTimeMatches = Array.from(filters.leadTimes).some(
        (filterLeadTime) =>
          supply.leadTime?.toLowerCase() === filterLeadTime.toLowerCase(),
      );

      if (!leadTimeMatches) {
        return false;
      }
    } else if (filters.leadTimes?.size > 0 && !supply.leadTime) {
      return false;
    }

    return true;
  });
}

export function areFiltersActive(filters: FilterState): boolean {
  return (
    filters.manufacturers.size > 0 ||
    filters.suppliers.size > 0 ||
    filters.packagings.size > 0 ||
    filters.stockRange.size > 0 ||
    filters.authorizationStatus.size > 0 ||
    (filters.leadTimes?.size ?? 0) > 0
  );
}
