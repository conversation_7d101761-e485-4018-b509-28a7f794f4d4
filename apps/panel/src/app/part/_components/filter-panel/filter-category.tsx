import { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface FilterCategoryProps {
  title?: string;
  items: string[];
  selectedItems: Set<string>;
  onChange: (value: string, checked: boolean) => void;
}

export function FilterCategory({
  title,
  items,
  selectedItems,
  onChange,
}: FilterCategoryProps) {
  const [isExpanded, setIsExpanded] = useState(items.length <= 5);
  const displayItems = isExpanded ? items : items.slice(0, 5);

  return (
    <div>
      {title && <h4 className="font-medium text-gray-700 mb-2">{title}</h4>}
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {displayItems.map((item) => (
          <div key={item} className="flex items-center space-x-2">
            <Checkbox
              id={`${title || "filter"}-${item}`}
              checked={selectedItems.has(item)}
              onCheckedChange={(checked: boolean | "indeterminate") =>
                onChange(item, checked === true)
              }
            />
            <Label
              htmlFor={`${title || "filter"}-${item}`}
              className="text-sm text-gray-700 truncate cursor-pointer"
              title={item}
            >
              {item}
            </Label>
          </div>
        ))}
      </div>

      {items.length > 5 && (
        <button
          type="button"
          onClick={() => setIsExpanded(!isExpanded)}
          className="mt-2 text-xs text-indigo-600 hover:text-indigo-800"
        >
          {isExpanded ? "Show Less" : `Show ${items.length - 5} More`}
        </button>
      )}
    </div>
  );
}
