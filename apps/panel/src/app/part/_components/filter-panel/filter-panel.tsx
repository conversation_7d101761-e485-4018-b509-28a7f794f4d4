import { Supply } from "@/app/part/_components";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { AuthorizationFilter } from "./authorization-filter";
import { FilterCategory } from "./filter-category";
import { useFilterPanel } from "./filter-panel.hook";
import { StockFilter } from "./stock-filter";

export interface FilterState {
  manufacturers: Set<string>;
  suppliers: Set<string>;
  packagings: Set<string>;
  stockRange: Set<string>;
  authorizationStatus: Set<string>;
  leadTimes: Set<string>;
}

interface FilterPanelProps {
  supplies: Supply[];
  onFilterChange: (filters: FilterState) => void;
}

export function FilterPanel({ supplies, onFilterChange }: FilterPanelProps) {
  const {
    filters,
    uniqueManufacturers,
    uniqueSuppliers,
    uniquePackagings,
    uniqueLeadTimes,
    handleFilterChange,
    handleStockRangeChange,
    handleAuthorizationStatusChange,
    handleLeadTimeChange,
  } = useFilterPanel({ onFilterChange, supplies });

  return (
    <div className="mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Manufacturer</CardTitle>
          </CardHeader>
          <CardContent>
            <FilterCategory
              items={uniqueManufacturers}
              selectedItems={filters.manufacturers}
              onChange={(value, checked) =>
                handleFilterChange("manufacturers", value, checked)
              }
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Distributors</CardTitle>
          </CardHeader>
          <CardContent>
            <FilterCategory
              items={uniqueSuppliers}
              selectedItems={filters.suppliers}
              onChange={(value, checked) =>
                handleFilterChange("suppliers", value, checked)
              }
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Packaging</CardTitle>
          </CardHeader>
          <CardContent>
            <FilterCategory
              items={uniquePackagings}
              selectedItems={filters.packagings}
              onChange={(value, checked) =>
                handleFilterChange("packagings", value, checked)
              }
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Stock</CardTitle>
          </CardHeader>
          <CardContent>
            <StockFilter
              selectedRanges={filters.stockRange}
              onChange={handleStockRangeChange}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Authorization Status</CardTitle>
          </CardHeader>
          <CardContent>
            <AuthorizationFilter
              selectedStatuses={filters.authorizationStatus}
              onChange={handleAuthorizationStatusChange}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Lead Time</CardTitle>
          </CardHeader>
          <CardContent>
            <FilterCategory
              items={uniqueLeadTimes}
              selectedItems={filters.leadTimes}
              onChange={handleLeadTimeChange}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
