import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { AUTHORIZATION_STATUS } from "./filter-panel.const";

interface AuthorizationFilterProps {
  selectedStatuses: Set<string>;
  onChange: (value: string, checked: boolean) => void;
}

export function AuthorizationFilter({
  selectedStatuses,
  onChange,
}: AuthorizationFilterProps) {
  return (
    <div className="space-y-2">
      {AUTHORIZATION_STATUS.map((status) => (
        <div key={status.id} className="flex items-center space-x-2">
          <Checkbox
            id={`authorization-${status.id}`}
            checked={selectedStatuses.has(status.id)}
            onCheckedChange={(checked: boolean | "indeterminate") =>
              onChange(status.id, checked === true)
            }
          />
          <Label
            htmlFor={`authorization-${status.id}`}
            className="text-sm text-gray-700 cursor-pointer"
          >
            {status.label}
          </Label>
        </div>
      ))}
    </div>
  );
}
