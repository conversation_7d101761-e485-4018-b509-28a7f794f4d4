"use client";

import { LocalPart } from "@/queries/products";
import BaseProductSearch from "./BaseProductSearch";

interface CompactProductSearchProps {
  onSelect: (selectedProduct: string, fullPart?: LocalPart) => void;
  placeholder?: string;
  className?: string;
}

export default function CompactProductSearch({
  onSelect,
  placeholder = "Find part...",
  className = "",
}: CompactProductSearchProps) {
  return (
    <BaseProductSearch
      onSelect={onSelect}
      className={className}
      renderInput={({ inputRef, value, onChange, onKeyDown, onFocus }) => (
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={onChange}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
          placeholder={placeholder}
          className="text-sm border-gray-300 p-2 w-full rounded-md focus:ring-blue-500 focus:border-blue-500"
        />
      )}
      renderSuggestions={({
        loading,
        suggestions,
        selectedIndex,
        onSelect,
      }) => (
        <div className="bg-white shadow-md text-sm rounded-md border border-gray-200">
          {loading ? (
            <div className="p-2 text-gray-500 flex items-center">
              <svg
                className="animate-spin h-3 w-3 mr-2 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <span>Loading...</span>
            </div>
          ) : suggestions.length > 0 ? (
            <ul>
              {suggestions.map((product, index) => (
                <li
                  key={index}
                  className={`p-2 cursor-pointer hover:bg-gray-50 ${
                    index === selectedIndex ? "bg-blue-50 text-blue-700" : ""
                  }`}
                  onMouseDown={() => onSelect(product.mpn, product)}
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{product.mpn}</span>
                    {product.description && (
                      <span className="text-xs text-gray-500 truncate">
                        {product.description}
                      </span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-2 text-gray-500 text-center">No parts found</div>
          )}
        </div>
      )}
    />
  );
}
