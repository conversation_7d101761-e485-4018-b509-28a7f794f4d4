"use client";

import { useEffect, useRef } from "react";

import { useProductSearch } from "@/hooks/useProductSearch";
import { LocalPart } from "@/queries/products";

type InputRefType = {
  current: HTMLInputElement | null;
};

interface BaseProductSearchProps {
  onSelect?: (selectedProduct: string, fullPart?: LocalPart) => void;
  limit?: number;
  initialValue?: string;
  renderInput: (props: {
    inputRef: InputRefType;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    onFocus: () => void;
  }) => React.ReactNode;
  renderSuggestions?: (props: {
    loading: boolean;
    suggestions: LocalPart[];
    selectedIndex: number;
    onSelect: (suggestion: string, fullPart: LocalPart) => void;
  }) => React.ReactNode;
  renderSubmitButton?: (props: { onClick: () => void }) => React.ReactNode;
  className?: string;
}

export default function BaseProductSearch({
  onSelect,
  limit = 5,
  initialValue,
  renderInput,
  renderSuggestions,
  renderSubmitButton,
  className = "",
}: BaseProductSearchProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  const {
    searchTerm,
    showSuggestions,
    suggestionsLoading,
    suggestions,
    selectedSuggestionIndex,
    handleInputChange,
    handleKeyDown,
    selectSuggestion,
    submitSearch,
    setShowSuggestions,
  } = useProductSearch({ initialValue, limit, onSelect });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        if (showSuggestions) {
          setShowSuggestions(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showSuggestions, setShowSuggestions]);

  const handleInputChangeEvent = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleInputChange(e.target.value);
  };

  const handleInputFocus = () => {
    if (searchTerm.trim()) {
      setShowSuggestions(true);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div className="flex">
        {renderInput({
          inputRef,
          onChange: handleInputChangeEvent,
          onFocus: handleInputFocus,
          onKeyDown: handleKeyDown,
          value: searchTerm,
        })}

        {renderSubmitButton && renderSubmitButton({ onClick: submitSearch })}
      </div>

      {showSuggestions && renderSuggestions && (
        <div ref={suggestionsRef} className="absolute z-10 mt-1 w-full">
          {renderSuggestions({
            loading: suggestionsLoading,
            onSelect: selectSuggestion,
            selectedIndex: selectedSuggestionIndex,
            suggestions,
          })}
        </div>
      )}
    </div>
  );
}
