import { <PERSON>ert<PERSON>ircle, ChevronDown, ChevronUp, Loader2, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

import { Product } from "./product-results";

interface QuoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (quantity: number) => Promise<void>;
  product: Product;
  supply: {
    sku: string;
    supplier: string;
    minOrderQuantity: number;
    stock: number;
  };
  defaultQuantity?: number;
}

export const QuoteModal = ({
  isOpen,
  onClose,
  onSubmit,
  product,
  supply,
  defaultQuantity,
}: QuoteModalProps) => {
  const [quantity, setQuantity] = useState<number>(defaultQuantity || 1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dialogRef = useRef<HTMLDialogElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Configure the dialog when the ref is available and when isOpen changes
  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    // When mounting, ensure the dialog is closed initially
    if (!dialog.open && isOpen) {
      // Only open if it should be open
      dialog.showModal();
    } else if (dialog.open && !isOpen) {
      // Close if it's open but should be closed
      dialog.close();
    }

    // Cleanup function to ensure dialog is closed when component unmounts
    return () => {
      if (dialog.open) {
        dialog.close();
      }
    };
  }, [isOpen]);

  // Don't render the dialog at all if it's not supposed to be open
  if (!isOpen) {
    return null;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (quantity < supply.minOrderQuantity) {
      setError(`Minimum order quantity is ${supply.minOrderQuantity}`);
      return;
    }
    setError(null);
    setIsSubmitting(true);

    try {
      await onSubmit(quantity);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create quote");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent<HTMLDialogElement>) => {
    const rect = dialogRef.current?.getBoundingClientRect();
    if (rect) {
      const isInDialog =
        rect.top <= e.clientY &&
        e.clientY <= rect.top + rect.height &&
        rect.left <= e.clientX &&
        e.clientX <= rect.left + rect.width;
      if (!isInDialog) {
        onClose();
      }
    }
  };

  const incrementQuantity = () => {
    setQuantity((prev) => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity((prev) => Math.max(1, prev - 1));
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 1;
    setQuantity(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "ArrowUp") {
      e.preventDefault();
      incrementQuantity();
    } else if (e.key === "ArrowDown") {
      e.preventDefault();
      decrementQuantity();
    }
  };

  return (
    <dialog
      ref={dialogRef}
      className="fixed inset-0 z-50 p-4 bg-transparent backdrop:bg-black/40 m-0 w-full h-full flex items-center justify-center"
      onClick={handleBackdropClick}
    >
      <div className="w-[672px] bg-white rounded-2xl shadow-xl transform transition-all">
        <div className="relative h-[600px] flex flex-col">
          {/* Header */}
          <div className="flex-none px-6 py-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-semibold text-gray-900">
                Create Quote
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full p-1"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 px-6 py-4 overflow-y-auto">
            <div className="space-y-4">
              {/* Part Details Card */}
              <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
                <h4 className="text-lg font-medium text-gray-900 mb-4">
                  Part Details
                </h4>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">
                          MPN
                        </label>
                        <p className="mt-1 text-base font-medium text-gray-900">
                          {product.mpn}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">
                          Supplier
                        </label>
                        <p className="mt-1 text-base font-medium text-gray-900">
                          {supply.supplier}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">
                          SKU
                        </label>
                        <p className="mt-1 text-base font-medium text-gray-900">
                          {supply.sku}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">
                          Stock
                        </label>
                        <p className="mt-1 text-base font-medium text-gray-900">
                          {supply.stock}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">
                          Min Order Quantity
                        </label>
                        <p className="mt-1 text-base font-medium text-gray-900">
                          {supply.minOrderQuantity}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quantity Input */}
              <div>
                <label
                  htmlFor="quantity"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Quantity
                </label>
                <div className="relative">
                  <div className="relative mt-2 rounded-md shadow-sm">
                    <input
                      ref={inputRef}
                      type="number"
                      name="quantity"
                      id="quantity"
                      min={1}
                      value={quantity}
                      onChange={handleQuantityChange}
                      onKeyDown={handleKeyDown}
                      className="block w-full rounded-lg border-0 py-3 pl-4 pr-24 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500 text-lg font-medium [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                      style={{ MozAppearance: "textfield" }}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center">
                      <div className="h-full flex flex-col border-l border-gray-300">
                        <button
                          type="button"
                          onClick={incrementQuantity}
                          className="flex-1 px-2 hover:bg-gray-50 flex items-center justify-center border-b border-gray-300"
                        >
                          <ChevronUp className="h-3 w-3 text-gray-400" />
                        </button>
                        <button
                          type="button"
                          onClick={decrementQuantity}
                          className="flex-1 px-2 hover:bg-gray-50 flex items-center justify-center"
                        >
                          <ChevronDown className="h-3 w-3 text-gray-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                  {quantity < supply.minOrderQuantity && (
                    <p className="mt-2 text-sm text-amber-600">
                      Minimum order quantity is {supply.minOrderQuantity}
                    </p>
                  )}
                </div>
              </div>

              {error && (
                <div className="rounded-lg bg-red-50 p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      <AlertCircle className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-red-800 break-words whitespace-pre-wrap">
                        {error}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex-none px-6 py-4 bg-gray-50 rounded-b-2xl border-t border-gray-100">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSubmit}
                disabled={isSubmitting || quantity < supply.minOrderQuantity}
                className="px-4 py-2.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" />
                    Creating Quote...
                  </>
                ) : (
                  "Create Quote"
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </dialog>
  );
};
