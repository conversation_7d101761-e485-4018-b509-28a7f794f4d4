import { AlertCircle } from "lucide-react";

import { Price } from "@/app/part/_components";
import { formatPrice } from "@/app/part/_utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ConvertedPriceTooltipProps {
  priceInfo: Price;
  isEstimate?: boolean;
}

export const ConvertedPriceTooltip = ({
  priceInfo,
  isEstimate = false,
}: ConvertedPriceTooltipProps) => {
  const isConverted =
    priceInfo.originalPrice !== null &&
    priceInfo.originalCurrency &&
    priceInfo.conversionRate !== null;

  const priceText = `$${formatPrice(priceInfo.unitPrice)}`;
  const alertIconColor = isEstimate ? "text-gray-500" : "text-gray-500";
  const baseStyles = isEstimate ? "cursor-help font-bold" : "";
  const spanStyles = `flex items-center ${baseStyles}`;

  if (isConverted) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className={spanStyles}>
              {priceText}
              <AlertCircle className={`ml-1 h-3 w-3 ${alertIconColor}`} />
            </span>
          </TooltipTrigger>
          <TooltipContent className="bg-white text-black border border-gray-200 shadow-md p-2">
            <div className="text-xs">
              <p>
                Original: {priceInfo.originalCurrency}
                {formatPrice(priceInfo.originalPrice!)}
              </p>
              <p>Conversion rate: {priceInfo.conversionRate}</p>
              <p>Converted: USD {formatPrice(priceInfo.unitPrice)}</p>
              {isEstimate && (
                <p className="mt-1 text-gray-500">
                  Based on quantity: {priceInfo.quantity}+
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (isEstimate) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className={baseStyles}>{priceText}</span>
          </TooltipTrigger>
          <TooltipContent className="bg-white text-black border border-gray-200 shadow-md p-2">
            <div className="text-xs">
              <p>Price for quantity: {priceInfo.quantity}+</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return <span className={baseStyles}>{priceText}</span>;
};
