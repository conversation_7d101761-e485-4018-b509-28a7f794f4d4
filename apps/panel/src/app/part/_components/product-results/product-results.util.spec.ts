import { ProductSupply as Supply } from "../../../queries/types";
import { findOverallMinimumPriceAndSupplier } from "./product-results.util";

const baseSupply: Omit<Supply, "prices" | "supplier"> = {
  authorized: true,
  leadTime: "1 week",
  manufacturer: "Test Manufacturer",
  minOrderQuantity: 1,
  packaging: null,
  sku: "SKU123",
  stock: 100,
  supplierDatasheetUrl: "",
  supplierImageUrl: "",
  supplierProductUrl: "",
};

describe("findOverallMinimumPriceAndSupplier", () => {
  it("should return -1 and null for an empty supplies array", () => {
    const supplies: Supply[] = [];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(-1);
    expect(result.supplier).toBeNull();
  });

  it("should return -1 and null if all prices are Number.MAX_VALUE", () => {
    const supplies: Supply[] = [
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: Number.MAX_VALUE }],
        supplier: "Supplier A",
      },
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: Number.MAX_VALUE }],
        supplier: "Supplier B",
      },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(-1);
    expect(result.supplier).toBeNull();
  });

  it("should return -1 and null if prices array is empty for all supplies", () => {
    const supplies: Supply[] = [
      { ...baseSupply, prices: [], supplier: "Supplier A" },
      { ...baseSupply, prices: [], supplier: "Supplier B" },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(-1);
    expect(result.supplier).toBeNull();
  });

  it("should find the minimum price and supplier correctly with one supply", () => {
    const supplies: Supply[] = [
      {
        ...baseSupply,
        prices: [
          { currency: "USD", quantity: 1, unitPrice: 100 },
          { currency: "USD", quantity: 2, unitPrice: 50 },
        ],
        supplier: "Supplier A",
      },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(50);
    expect(result.supplier).toBe("Supplier A");
  });

  it("should find the minimum price and supplier correctly with multiple supplies", () => {
    const supplies: Supply[] = [
      {
        ...baseSupply,
        prices: [
          { currency: "USD", quantity: 1, unitPrice: 100 },
          { currency: "USD", quantity: 1, unitPrice: 200 },
        ],
        supplier: "Supplier A",
      },
      {
        ...baseSupply,
        prices: [
          { currency: "USD", quantity: 1, unitPrice: 75 },
          { currency: "USD", quantity: 1, unitPrice: 150 },
        ],
        supplier: "Supplier B",
      },
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 5, unitPrice: 60 }],
        supplier: "Supplier C",
      },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(60);
    expect(result.supplier).toBe("Supplier C");
  });

  it("should ignore non-numeric prices and Number.MAX_VALUE", () => {
    const supplies: Supply[] = [
      {
        ...baseSupply,
        prices: [
          {
            currency: "USD",
            quantity: 1,
            unitPrice: "invalid" as unknown as number,
          },
          { currency: "USD", quantity: 1, unitPrice: Number.MAX_VALUE },
          { currency: "USD", quantity: 1, unitPrice: 150 },
        ],
        supplier: "Supplier A",
      },
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: 120 }],
        supplier: "Supplier B",
      },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(120);
    expect(result.supplier).toBe("Supplier B");
  });

  it("should handle a case where the minimum price is 0", () => {
    const supplies: Supply[] = [
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: 10 }],
        supplier: "Supplier A",
      },
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: 0 }],
        supplier: "Supplier B",
      },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(0);
    expect(result.supplier).toBe("Supplier B");
  });

  it("should return -1 and null if all prices are invalid or Number.MAX_VALUE", () => {
    const supplies: Supply[] = [
      {
        ...baseSupply,
        prices: [
          {
            currency: "USD",
            quantity: 1,
            unitPrice: "invalid" as unknown as number,
          },
          { currency: "USD", quantity: 1, unitPrice: Number.MAX_VALUE },
        ],
        supplier: "Supplier A",
      },
      {
        ...baseSupply,
        prices: [
          {
            currency: "USD",
            quantity: 1,
            unitPrice: undefined as unknown as number,
          },
          {
            currency: "USD",
            quantity: 1,
            unitPrice: null as unknown as number,
          },
        ],
        supplier: "Supplier B",
      },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(-1);
    expect(result.supplier).toBeNull();
  });

  it("should correctly identify the supplier when multiple suppliers have the same minimum price (picks first encountered)", () => {
    const supplies: Supply[] = [
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: 50 }],
        supplier: "Supplier X",
      },
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: 50 }],
        supplier: "Supplier Y",
      },
      {
        ...baseSupply,
        prices: [{ currency: "USD", quantity: 1, unitPrice: 100 }],
        supplier: "Supplier Z",
      },
    ];
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(50);
    expect(result.supplier).toBe("Supplier X");
  });

  it("should find the minimum price and supplier from real backend data (first product)", () => {
    const result = findOverallMinimumPriceAndSupplier(supplies);
    expect(result.minPrice).toBe(0.117);
    expect(result.supplier).toBe("Future Electronics");
  });

  it("should return the minimum price in any price break", () => {
    const result = findOverallMinimumPriceAndSupplier([
      ...supplies,
      {
        authorized: true,
        leadTime: "10",
        manufacturer: "STMicroelectronics",
        minOrderQuantity: 1,
        packaging: "Cut Tape (CT)",
        prices: [
          { currency: "USD", quantity: 1, unitPrice: 0.57 },
          { currency: "USD", quantity: 10, unitPrice: 0.01 },
          { currency: "USD", quantity: 25, unitPrice: 0.362 },
          { currency: "USD", quantity: 100, unitPrice: 0.3155 },
          { currency: "USD", quantity: 250, unitPrice: 0.29328 },
          { currency: "USD", quantity: 500, unitPrice: 0.2799 },
          { currency: "USD", quantity: 1000, unitPrice: 0.26886 },
        ],
        sku: "497-LM317LZ-APCT-ND",
        stock: 1520,
        supplier: "DigiKey",
        supplierDatasheetUrl: "",
        supplierImageUrl: "",
        supplierProductUrl: "",
      },
    ]);
    expect(result.minPrice).toBe(0.01);
    expect(result.supplier).toBe("DigiKey");
  });
});

const supplies: Supply[] = [
  {
    authorized: true,
    leadTime: "10",
    manufacturer: "STMicroelectronics",
    minOrderQuantity: 1,
    packaging: "Cut Tape (CT)",
    prices: [
      { currency: "USD", quantity: 1, unitPrice: 0.57 },
      { currency: "USD", quantity: 10, unitPrice: 0.404 },
      { currency: "USD", quantity: 25, unitPrice: 0.362 },
      { currency: "USD", quantity: 100, unitPrice: 0.3155 },
      { currency: "USD", quantity: 250, unitPrice: 0.29328 },
      { currency: "USD", quantity: 500, unitPrice: 0.2799 },
      { currency: "USD", quantity: 1000, unitPrice: 0.26886 },
    ],
    sku: "497-LM317LZ-APCT-ND",
    stock: 1520,
    supplier: "DigiKey",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
  {
    authorized: true,
    leadTime: "10",
    manufacturer: "STMicroelectronics",
    minOrderQuantity: 2000,
    packaging: "Tape & Box (TB)",
    prices: [{ currency: "USD", quantity: 2000, unitPrice: 0.22294 }],
    sku: "497-LM317LZ-APTB-ND",
    stock: 14000,
    supplier: "DigiKey",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
  {
    authorized: true,
    leadTime: "70 Days",
    manufacturer: "STMicroelectronics",
    minOrderQuantity: 1,
    packaging: null,
    prices: [
      { currency: "USD", quantity: 1, unitPrice: 0.57 },
      { currency: "USD", quantity: 10, unitPrice: 0.381 },
      { currency: "USD", quantity: 25, unitPrice: 0.358 },
      { currency: "USD", quantity: 100, unitPrice: 0.304 },
      { currency: "USD", quantity: 250, unitPrice: 0.279 },
      { currency: "USD", quantity: 1000, unitPrice: 0.24 },
      { currency: "USD", quantity: 2000, unitPrice: 0.222 },
      { currency: "USD", quantity: 10000, unitPrice: 0.221 },
    ],
    sku: "511-LM317LZ-AP",
    stock: 4682,
    supplier: "Mouser",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
  {
    authorized: true,
    leadTime: "10 Weeks",
    manufacturer: "STMicroelectronics",
    minOrderQuantity: 8000,
    packaging: "AMMO",
    prices: [
      { currency: "USD", quantity: 2000, unitPrice: 0.123 },
      { currency: "USD", quantity: 4000, unitPrice: 0.122 },
      { currency: "USD", quantity: 8000, unitPrice: 0.12 },
      { currency: "USD", quantity: 30000, unitPrice: 0.117 },
    ],
    sku: "2022356",
    stock: 0,
    supplier: "Future Electronics",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
  {
    authorized: true,
    leadTime: "10 Weeks",
    manufacturer: "STMicroelectronics",
    minOrderQuantity: 8000,
    packaging: "AMMO",
    prices: [
      { currency: "USD", quantity: 2000, unitPrice: 0.123 },
      { currency: "USD", quantity: 4000, unitPrice: 0.122 },
      { currency: "USD", quantity: 8000, unitPrice: 0.12 },
      { currency: "USD", quantity: 30000, unitPrice: 0.117 },
    ],
    sku: "7741846",
    stock: 0,
    supplier: "Future Electronics",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
  {
    authorized: true,
    leadTime: "99 days",
    manufacturer: "STMICROELECTRONICS",
    minOrderQuantity: 5,
    packaging: "EACH",
    prices: [
      { currency: "USD", quantity: 5, unitPrice: 0.7541184034401711 },
      { currency: "USD", quantity: 10, unitPrice: 0.5043265633069488 },
      { currency: "USD", quantity: 100, unitPrice: 0.4015640974293574 },
      { currency: "USD", quantity: 500, unitPrice: 0.3699448771593292 },
      { currency: "USD", quantity: 1000, unitPrice: 0.3130302806732786 },
      { currency: "USD", quantity: 5000, unitPrice: 0.2940587485112617 },
      { currency: "USD", quantity: 10000, unitPrice: 0.29247778749776027 },
    ],
    sku: "3130111",
    stock: 1611,
    supplier: "Farnell",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
  {
    authorized: true,
    leadTime: "105 days",
    manufacturer: "STMICROELECTRONICS",
    minOrderQuantity: 1,
    packaging: "EACH",
    prices: [
      { currency: "USD", quantity: 1, unitPrice: 0.635 },
      { currency: "USD", quantity: 10, unitPrice: 0.446 },
      { currency: "USD", quantity: 100, unitPrice: 0.369 },
      { currency: "USD", quantity: 500, unitPrice: 0.344 },
      { currency: "USD", quantity: 1000, unitPrice: 0.301 },
      { currency: "USD", quantity: 4000, unitPrice: 0.287 },
      { currency: "USD", quantity: 10000, unitPrice: 0.273 },
    ],
    sku: "07AH6658",
    stock: 2940,
    supplier: "Newark",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
  {
    authorized: true,
    leadTime: "104 days",
    manufacturer: "STMICROELECTRONICS",
    minOrderQuantity: 5,
    packaging: "EACH",
    prices: [
      { currency: "USD", quantity: 5, unitPrice: 0.6804708150516986 },
      { currency: "USD", quantity: 10, unitPrice: 0.4319655419078185 },
      { currency: "USD", quantity: 100, unitPrice: 0.35107624158917294 },
      { currency: "USD", quantity: 500, unitPrice: 0.3343980353379058 },
      { currency: "USD", quantity: 1000, unitPrice: 0.26518347939514725 },
      { currency: "USD", quantity: 5000, unitPrice: 0.2601800175197671 },
      { currency: "USD", quantity: 10000, unitPrice: 0.255176555644387 },
    ],
    sku: "3130111",
    stock: 1611,
    supplier: "Element14",
    supplierDatasheetUrl: "",
    supplierImageUrl: "",
    supplierProductUrl: "",
  },
];
