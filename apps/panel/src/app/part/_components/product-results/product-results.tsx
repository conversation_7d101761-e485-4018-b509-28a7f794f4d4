import {
  <PERSON>,
  flexR<PERSON>,
  <PERSON><PERSON>,
  <PERSON>erG<PERSON>,
  Row,
} from "@tanstack/react-table";
import {
  ArrowDownIcon,
  ArrowUpDownIcon,
  ArrowUpIcon,
  BadgePercent,
  ClockIcon,
} from "lucide-react";
import { useState } from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ToggleSwitch } from "@/components/ui/toggle-switch";

import { AddToBom } from "../add-to-bom/add-to-bom";
import { useProductResults } from "./product-results.hook";
import { Product, Supply } from "./product-results.type";

interface ProductResultsProps {
  product: Product;
  showQuoteButton?: boolean;
}

interface ProductHeaderProps {
  mpn: string;
  description: string;
  leadTime: string;
  showAllPrices: boolean;
  setShowAllPrices: (show: boolean) => void;
  productImage: string | null;
  showAddToBom: boolean;
  minPrice: number;
  minPriceSupplier: string | null;
}

const ProductHeader = ({
  mpn,
  description,
  leadTime,
  showAllPrices,
  setShowAllPrices,
  productImage,
  showAddToBom,
  minPrice,
  minPriceSupplier,
}: ProductHeaderProps) => (
  <div className="p-4 border-b border-gray-200">
    <div className="flex items-start gap-4">
      <div className="flex-shrink-0">
        {productImage ? (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={productImage}
            alt={`${mpn} image`}
            className="w-24 h-24 object-contain border border-gray-200 rounded"
          />
        ) : (
          <div className="w-24 h-24 bg-gray-100 flex items-center justify-center text-gray-400 border border-gray-200 rounded">
            <svg
              className="w-12 h-12"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>
      <div className="flex-grow">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-semibold text-gray-800">{mpn}</h2>
          <div className="flex items-center gap-3">
            {showAddToBom && <AddToBom mpn={mpn} />}
            <div className="scale-90 origin-right">
              <ToggleSwitch
                label="Show All Prices"
                checked={showAllPrices}
                onChange={() => setShowAllPrices(!showAllPrices)}
              />
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1 mt-1">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-gray-800">{description}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-700">
            <ClockIcon className="h-4 w-4 text-gray-500" />
            <span className="font-medium">Lead Time:</span>
            <span>{leadTime} weeks</span>
            {minPrice !== -1 && (
              <>
                <span className="mx-2 text-gray-300">|</span>
                <BadgePercent className="h-4 w-4 text-green-600" />
                <span className="font-medium">Min Price:</span>
                <span className="font-semibold text-green-700">
                  ${minPrice.toFixed(3)}
                </span>
                <span className="text-gray-500">
                  ({minPriceSupplier || "N/A"})
                </span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  </div>
);

const ProductResults = ({
  product,
  showQuoteButton = true,
}: ProductResultsProps) => {
  const [showAllPrices, setShowAllPrices] = useState(false);

  const { columns, productImage, table, overallMinPriceData } =
    useProductResults({
      product,
      showAllPrices,
      showQuoteButton,
    });

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6 w-full">
      <ProductHeader
        mpn={product.mpn}
        description={product.description}
        leadTime={product.leadTime}
        showAllPrices={showAllPrices}
        setShowAllPrices={setShowAllPrices}
        productImage={productImage}
        showAddToBom={!showQuoteButton}
        minPrice={overallMinPriceData.minPrice}
        minPriceSupplier={overallMinPriceData.supplier}
      />
      <div className="max-w-full overflow-x-auto">
        <div>
          <Table className="text-xs">
            <TableHeader>
              {table
                .getHeaderGroups()
                .map((headerGroup: HeaderGroup<Supply>) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(
                      (header: Header<Supply, unknown>) => (
                        <TableHead
                          key={header.id}
                          onClick={
                            header.column.getCanSort()
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                          className={`h-8 px-2 py-1 ${
                            header.column.getCanSort()
                              ? "cursor-pointer hover:bg-gray-100"
                              : ""
                          }`}
                        >
                          <div className="flex items-center">
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext(),
                                )}
                            {header.column.getCanSort() && (
                              <>
                                {header.column.getIsSorted() === "asc" ? (
                                  <ArrowUpIcon className="ml-1 h-3 w-3" />
                                ) : header.column.getIsSorted() === "desc" ? (
                                  <ArrowDownIcon className="ml-1 h-3 w-3" />
                                ) : (
                                  <div className="ml-1 h-3 w-3">
                                    <ArrowUpDownIcon className="h-3 w-3 text-gray-300" />
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </TableHead>
                      ),
                    )}
                  </TableRow>
                ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table
                  .getRowModel()
                  .rows.map((row: Row<Supply>, index: number) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className={index % 2 === 1 ? "bg-gray-50" : ""}
                    >
                      {row
                        .getVisibleCells()
                        .map((cell: Cell<Supply, unknown>) => (
                          <TableCell key={cell.id} className="px-2 py-2">
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </TableCell>
                        ))}
                    </TableRow>
                  ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-16 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default ProductResults;
