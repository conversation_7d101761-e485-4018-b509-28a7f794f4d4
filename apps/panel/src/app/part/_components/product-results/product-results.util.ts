import { Price, ProductSupply as Supply } from "@/queries/types";

export const getNumericPriceForQuantity = (
  prices: Price[],
  quantity: number,
): number => {
  const price = prices.find((p) => p.quantity === quantity);
  if (price) {
    return price.unitPrice;
  }

  const sortedPrices = [...prices]
    .filter((p) => p.quantity < quantity)
    .sort((a, b) => a.quantity - b.quantity);

  if (sortedPrices.length === 0) {
    return Number.MAX_VALUE;
  }

  const closestPrice = sortedPrices.reduce((prev, curr) => {
    const prevDiff = Math.abs(prev.quantity - quantity);
    const currDiff = Math.abs(curr.quantity - quantity);
    return currDiff < prevDiff ? curr : prev;
  });

  return closestPrice.unitPrice;
};

export const getAllPrices = (prices: Price[]): Price[] =>
  [...prices].sort((a, b) => a.quantity - b.quantity);

export const findOverallMinimumPriceAndSupplier = (
  supplies: Supply[],
): { minPrice: number; supplier: string | null } => {
  let overallMinPrice = Number.MAX_VALUE;
  let supplierWithMinPrice: string | null = null;

  supplies.forEach((supply) => {
    supply.prices.forEach((priceEntry) => {
      const price = priceEntry.unitPrice;
      if (
        typeof price === "number" &&
        price < overallMinPrice &&
        price !== Number.MAX_VALUE
      ) {
        overallMinPrice = price;
        supplierWithMinPrice = supply.supplier;
      }
    });
  });

  return {
    minPrice: overallMinPrice === Number.MAX_VALUE ? -1 : overallMinPrice,
    supplier: supplierWithMinPrice,
  };
};
