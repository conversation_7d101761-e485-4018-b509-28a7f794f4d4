import {
  ColumnDef,
  createColumnHelper,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";

import { AuthorizationBadge } from "./authorization-badge";
import { PriceDisplay } from "./price-display";
import { PriceList } from "./price-list";
import { Product, Supply } from "./product-results.type";
import {
  findOverallMinimumPriceAndSupplier,
  getNumericPriceForQuantity,
} from "./product-results.util";
import { QuoteButtonCell } from "./quote-button-cell";

const columnHelper = createColumnHelper<Supply>();

const useProductColumns = (
  supplies: Supply[],
  product: Product,
  standardPriceBreaks: number[],
  showQuoteButton: boolean = true,
  showAllPrices: boolean = false,
): (
  | ColumnDef<Supply, string>
  | ColumnDef<Supply, number>
  | ColumnDef<Supply, unknown>
)[] =>
  useMemo(() => {
    const minPrices = standardPriceBreaks.reduce(
      (acc, qty) => {
        let minPrice = Number.MAX_VALUE;
        supplies.forEach((supply: Supply) => {
          const price = getNumericPriceForQuantity(supply.prices, qty);
          if (price < minPrice) {
            minPrice = price;
          }
        });
        acc[qty] = minPrice;
        return acc;
      },
      {} as Record<number, number>,
    );

    const priceBreakColumns = standardPriceBreaks.map((qty) =>
      columnHelper.accessor(
        (row) => getNumericPriceForQuantity(row.prices, qty),
        {
          cell: ({ row }) => {
            const currentPrice = getNumericPriceForQuantity(
              row.original.prices,
              qty,
            );
            const isMinPrice =
              currentPrice !== Number.MAX_VALUE &&
              currentPrice === minPrices[qty];
            const cellClass = isMinPrice
              ? "whitespace-nowrap text-xs text-gray-900 bg-green-100 p-1 rounded"
              : "whitespace-nowrap text-xs text-gray-900";

            return (
              <div className={cellClass}>
                <PriceDisplay prices={row.original.prices} quantity={qty} />
              </div>
            );
          },
          header: () => <div>{qty}+</div>,
          id: `price_${qty}`,
          sortingFn: "basic",
        },
      ),
    );

    const allColumns = [
      columnHelper.display({
        cell: ({ row }) => (
          <div className="whitespace-nowrap text-xs text-gray-900">
            {row.index + 1}
          </div>
        ),
        enableSorting: true,
        header: "#",
        id: "rowNumber",
      }),
      columnHelper.accessor("supplier", {
        cell: ({ row }) => (
          <div className="whitespace-nowrap text-xs text-blue-600 hover:text-blue-800 flex flex-col items-start">
            <div className="flex items-center">
              <AuthorizationBadge isAuthorized={row.original.authorized} />
              <a
                href={row.original.supplierProductUrl}
                target="_blank"
                rel="noopener noreferrer"
              >
                {row.original.supplier}
              </a>
            </div>
            <div className="whitespace-nowrap text-[10px] text-gray-900 mt-0.5">
              {row.original.sku}
            </div>
          </div>
        ),
        enableSorting: false,
        header: "Distributor",
      }),
      columnHelper.accessor("stock", {
        cell: ({ getValue }) => (
          <div className="whitespace-nowrap text-xs text-gray-900">
            {getValue()}
          </div>
        ),
        enableSorting: false,
        header: "Stock",
      }),
      columnHelper.accessor("minOrderQuantity", {
        cell: ({ getValue }) => (
          <div className="whitespace-nowrap text-xs text-gray-900">
            {getValue()}
          </div>
        ),
        enableSorting: false,
        header: "MOQ",
      }),
      columnHelper.accessor("packaging", {
        cell: ({ getValue }) => (
          <div className="whitespace-nowrap text-xs text-gray-900">
            {getValue() || "-"}
          </div>
        ),
        enableSorting: false,
        header: "Pkg",
      }),
      columnHelper.accessor("leadTime", {
        cell: ({ getValue }) => {
          const value = getValue();
          return (
            <div className="whitespace-nowrap text-xs text-gray-900">
              {value ? `${value}` : "-"}
            </div>
          );
        },
        enableSorting: true,
        header: "Lead Time (weeks)",
      }),
      ...priceBreakColumns,
      columnHelper.display({
        cell: ({ row }) => (
          <div className="text-xs text-gray-900 min-w-[120px]">
            <div>
              <PriceList
                prices={row.original.prices}
                defaultExpanded={showAllPrices}
              />
            </div>
          </div>
        ),
        enableSorting: false,
        header: "All Price Breaks",
        id: "allPrices",
      }),
    ];

    if (showQuoteButton) {
      allColumns.push(
        columnHelper.display({
          cell: ({ row }) => (
            <div className="whitespace-nowrap text-xs">
              <QuoteButtonCell product={product} supply={row.original} />
            </div>
          ),
          enableSorting: false,
          header: "Quote",
          id: "quote",
        }),
      );
    }

    return allColumns;
  }, [supplies, product, standardPriceBreaks, showQuoteButton, showAllPrices]);

const standardPriceBreaks = [1, 10, 100, 1000, 10000, 100000, 1000000];

export const useProductResults = ({
  product,
  showQuoteButton = true,
  showAllPrices = false,
}: {
  product: Product;
  showQuoteButton?: boolean;
  showAllPrices?: boolean;
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);

  const productImage =
    product.supplies.find((supply: Supply) => supply.supplierImageUrl)
      ?.supplierImageUrl || null;

  const columns = useProductColumns(
    product.supplies,
    product,
    standardPriceBreaks,
    showQuoteButton,
    showAllPrices,
  );

  const overallMinPriceData = useMemo(
    () => findOverallMinimumPriceAndSupplier(product.supplies),
    [product.supplies],
  );

  const table = useReactTable({
    columns,
    data: product.supplies,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  return {
    columns,
    overallMinPriceData,
    productImage,
    setSorting,
    showAllPrices,
    sorting,
    table,
  };
};
