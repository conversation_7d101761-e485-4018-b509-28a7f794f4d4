import {
  <PERSON>,
  flex<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>erG<PERSON>,
  <PERSON>,
} from "@tanstack/react-table";
import {
  ArrowDownIcon,
  ArrowUpDownIcon,
  ArrowUpIcon,
  BadgePercent,
  CircuitBoard,
  ClockIcon,
  Download,
} from "lucide-react";
import Link from "next/link";
import { useQueryState } from "nuqs";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ToggleSwitch } from "@/components/ui/toggle-switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ManufacturerGroup, ProductGrouped } from "@/queries/products";
import { ProductSupply } from "@/queries/types";

import { AddToBom } from "../add-to-bom";
import { CrossesButton } from "./crosses-button/crosses-button";
import { useProductResults } from "./product-results.hook";
import { findOverallMinimumPriceAndSupplier } from "./product-results.util";

interface ProductResultsGroupedProps {
  product: ProductGrouped;
  showQuoteButton?: boolean;
  disableCrossesButton?: boolean;
  disableTrendButton?: boolean;
}

interface ProductHeaderProps {
  mpn: string;
  description: string;
  leadTime: string;
  showAllPrices: boolean;
  setShowAllPrices: (show: boolean) => void;
  productImage: string | null;
  showAddToBom: boolean;
  minPrice: number;
  minPriceSupplier: string | null;
  manufacturer: string;
  datasheetUrl?: string;
  disableCrossesButton?: boolean;
  disableTrendButton?: boolean;
}

const ProductHeader = ({
  mpn,
  description,
  leadTime,
  showAllPrices,
  setShowAllPrices,
  productImage,
  showAddToBom,
  minPrice,
  minPriceSupplier,
  manufacturer,
  datasheetUrl,
  disableCrossesButton,
  disableTrendButton,
}: ProductHeaderProps) => {
  const [searchTerm] = useQueryState("product");

  return (
    <div className="p-4 border-b border-gray-200 bg-gray-50">
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          {productImage ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={productImage}
              alt={`${mpn} image`}
              className="w-24 h-24 object-contain border border-gray-200 rounded"
            />
          ) : (
            <div className="w-24 h-24 bg-gray-100 flex items-center justify-center text-gray-400 border border-gray-200 rounded">
              <CircuitBoard className="w-12 h-12" />
            </div>
          )}
        </div>
        <div className="flex-grow">
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center gap-3">
              <Link
                href={`/part/${encodeURIComponent(mpn)}?search-term=${searchTerm}`}
              >
                {" "}
                <h2 className="text-xl font-semibold text-blue-800 underline">
                  {mpn}
                </h2>
              </Link>

              <span className="text-sm text-gray-500 font-medium px-2 py-1 rounded">
                {manufacturer}
              </span>
              {!disableTrendButton && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                        className="h-8 px-3 text-xs font-medium border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors"
                      >
                        <Link
                          href={`/trend/product/by-part-number/${encodeURIComponent(mpn)}`}
                          target="_blank"
                        >
                          Part Trend
                        </Link>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View part trend</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {!disableCrossesButton && (
                <CrossesButton
                  mpn={mpn}
                  manufacturer={manufacturer}
                  mainProduct={{
                    dataSheet: datasheetUrl,
                    description,
                    manufacturer,
                    mpn,
                    package: undefined,
                    partLifecycle: undefined,
                    roHsFlag: undefined,
                  }}
                />
              )}
            </div>
            <div className="flex items-center gap-3">
              {showAddToBom && (
                <AddToBom mpn={mpn} manufacturerName={manufacturer} />
              )}
              <div className="scale-90 origin-right">
                <ToggleSwitch
                  label="Show All Prices"
                  checked={showAllPrices}
                  onChange={() => setShowAllPrices(!showAllPrices)}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-1 mt-1">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-gray-800">{description}</span>
              {datasheetUrl && (
                <Badge
                  onClick={() => window.open(datasheetUrl, "_blank")}
                  variant="outline"
                  className="cursor-pointer"
                >
                  <Download className="h-3 w-3 mr-1" />
                  <span>Datasheet</span>
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-700">
              <ClockIcon className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Lead Time:</span>
              <span>{leadTime} weeks</span>
              {minPrice !== -1 && (
                <>
                  <span className="mx-2 text-gray-300">|</span>
                  <BadgePercent className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Min Price:</span>
                  <span className="font-semibold text-green-700">
                    ${minPrice.toFixed(4)}
                  </span>
                  <span className="text-gray-500">
                    ({minPriceSupplier || "N/A"})
                  </span>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface ManufacturerProductTableProps {
  supplies: ProductSupply[];
  showQuoteButton: boolean;
  showAllPrices: boolean;
}

const ManufacturerProductTable = ({
  supplies,
  showQuoteButton,
  showAllPrices,
}: ManufacturerProductTableProps) => {
  const mockProduct = {
    description: "",
    leadTime: "",
    mpn: "",
    supplies,
  };

  const { columns, table } = useProductResults({
    product: mockProduct,
    showAllPrices,
    showQuoteButton,
  });

  return (
    <div className="max-w-full overflow-x-auto">
      <Table className="text-xs">
        <TableHeader>
          {table
            .getHeaderGroups()
            .map((headerGroup: HeaderGroup<ProductSupply>) => (
              <TableRow key={headerGroup.id} className="bg-gray-50">
                {headerGroup.headers.map(
                  (header: Header<ProductSupply, unknown>) => (
                    <TableHead
                      key={header.id}
                      onClick={
                        header.column.getCanSort()
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                      className={`h-8 px-2 py-1 ${
                        header.column.getCanSort()
                          ? "cursor-pointer hover:bg-gray-100"
                          : ""
                      }`}
                    >
                      <div className="flex items-center">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                        {header.column.getCanSort() && (
                          <>
                            {header.column.getIsSorted() === "asc" ? (
                              <ArrowUpIcon className="ml-1 h-3 w-3" />
                            ) : header.column.getIsSorted() === "desc" ? (
                              <ArrowDownIcon className="ml-1 h-3 w-3" />
                            ) : (
                              <div className="ml-1 h-3 w-3">
                                <ArrowUpDownIcon className="h-3 w-3 text-gray-300" />
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </TableHead>
                  ),
                )}
              </TableRow>
            ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table
              .getRowModel()
              .rows.map((row: Row<ProductSupply>, index: number) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={index % 2 === 1 ? "bg-gray-50" : ""}
                >
                  {row
                    .getVisibleCells()
                    .map((cell: Cell<ProductSupply, unknown>) => (
                      <TableCell key={cell.id} className="px-2 py-2">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                </TableRow>
              ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-16 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

const ProductResultsGrouped = ({
  product,
  showQuoteButton = false,
  disableCrossesButton = false,
  disableTrendButton = false,
}: ProductResultsGroupedProps) => {
  const [showAllPrices, setShowAllPrices] = useState(false);

  return (
    <>
      {product.manufacturerGroups.map(
        (group: ManufacturerGroup, groupIndex: number) => {
          const productImage =
            group.supplies.find((supply) => supply.supplierImageUrl)
              ?.supplierImageUrl || null;

          const { minPrice, supplier: minPriceSupplier } =
            findOverallMinimumPriceAndSupplier(group.supplies);

          const datasheetUrl =
            group.supplies.find((supply) => supply.supplierDatasheetUrl)
              ?.supplierDatasheetUrl || undefined;

          return (
            <div
              key={`${product.mpn}-${group.manufacturer}-${groupIndex}`}
              className="bg-white shadow-md rounded-lg overflow-hidden mb-6 w-full"
            >
              <ProductHeader
                mpn={product.mpn}
                description={product.description}
                leadTime={product.leadTime}
                showAllPrices={showAllPrices}
                setShowAllPrices={setShowAllPrices}
                productImage={productImage}
                showAddToBom={!showQuoteButton}
                minPrice={minPrice}
                minPriceSupplier={minPriceSupplier}
                manufacturer={group.manufacturer}
                datasheetUrl={datasheetUrl}
                disableCrossesButton={disableCrossesButton}
                disableTrendButton={disableTrendButton}
              />
              <ManufacturerProductTable
                supplies={group.supplies}
                showQuoteButton={showQuoteButton}
                showAllPrices={showAllPrices}
              />
            </div>
          );
        },
      )}
    </>
  );
};

export default ProductResultsGrouped;
