import { Price } from "@/app/part/_components";

import { ConvertedPriceTooltip } from "./converted-price-tooltip";
import { getNumericPriceForQuantity } from "./product-results.util";

interface PriceDisplayProps {
  prices: Price[];
  quantity: number;
}

export const PriceDisplay = ({ prices, quantity }: PriceDisplayProps) => {
  const numericPrice = getNumericPriceForQuantity(prices, quantity);

  if (numericPrice === Number.MAX_VALUE) {
    return <span>-</span>;
  }

  const exactPrice = prices.find((p) => p.quantity === quantity);
  if (exactPrice) {
    return <ConvertedPriceTooltip priceInfo={exactPrice} />;
  }

  const sortedPrices = [...prices]
    .filter((p) => p.quantity < quantity)
    .sort((a, b) => a.quantity - b.quantity);

  if (sortedPrices.length === 0) {
    return <span>-</span>;
  }

  const closestPrice = sortedPrices.reduce((prev, curr) => {
    const prevDiff = Math.abs(prev.quantity - quantity);
    const currDiff = Math.abs(curr.quantity - quantity);
    return currDiff < prevDiff ? curr : prev;
  });

  return <ConvertedPriceTooltip priceInfo={closestPrice} isEstimate={true} />;
};
