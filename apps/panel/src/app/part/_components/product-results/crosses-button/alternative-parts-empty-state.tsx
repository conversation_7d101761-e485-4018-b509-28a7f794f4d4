import { Package } from "lucide-react";

interface AlternativePartsEmptyStateProps {
  mpn: string;
}

export const AlternativePartsEmptyState = ({
  mpn,
}: AlternativePartsEmptyStateProps) => (
  <div className="flex flex-col items-center justify-center py-12">
    <Package className="h-16 w-16 text-gray-300 mb-4" />
    <h3 className="text-lg font-medium text-gray-900 mb-2">
      No Alternative Parts Found
    </h3>
    <p className="text-gray-500 text-center max-w-md">
      No alternative parts found for this MPN:
      <span className="font-mono text-gray-700 ml-1">{mpn}</span>
    </p>
  </div>
);
