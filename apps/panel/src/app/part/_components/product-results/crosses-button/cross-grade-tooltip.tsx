import { ReactNode } from "react";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CrossGradeTooltipProps {
  crossComment: string;
  children: ReactNode;
}

export const CrossGradeTooltip = ({
  crossComment,
  children,
}: CrossGradeTooltipProps) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent>
        <p>{crossComment && crossComment}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);
