import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { useCrossesButton } from "./crosses-button.hook";
import { MainProductData } from "./crosses-button.types";
import { CrossesModal } from "./crosses-modal";

interface CrossesButtonProps {
  mpn: string;
  manufacturer: string;
  mainProduct: MainProductData;
}

export const CrossesButton = ({
  mpn,
  manufacturer,
  mainProduct,
}: CrossesButtonProps) => {
  const { isModalOpen, closeModal, handleShowAlternatives } =
    useCrossesButton();

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={handleShowAlternatives}
              className="h-8 px-3 text-xs font-medium border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors"
            >
              Crosses
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>View alternative parts</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <CrossesModal
        isOpen={isModalOpen}
        onClose={closeModal}
        mpn={mpn}
        manufacturer={manufacturer}
        mainProduct={mainProduct}
      />
    </>
  );
};
