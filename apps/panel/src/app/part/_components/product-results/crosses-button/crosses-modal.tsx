import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { AlternativePartsTable } from "./alternative-parts-table";
import { MainProductData } from "./crosses-button.types";

interface CrossesModalProps {
  isOpen: boolean;
  onClose: () => void;
  mpn: string;
  manufacturer: string;
  mainProduct: MainProductData;
}

export const CrossesModal = ({
  isOpen,
  onClose,
  mpn,
  manufacturer,
  mainProduct,
}: CrossesModalProps) => (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          Alternative Parts for
          <span className="font-mono text-blue-600">{mpn}</span>
          <span className="text-gray-500">({manufacturer})</span>
        </DialogTitle>
      </DialogHeader>

      <div className="flex-1 overflow-hidden">
        <AlternativePartsTable
          mpn={mpn}
          manufacturer={manufacturer}
          mainProduct={mainProduct}
        />
      </div>
    </DialogContent>
  </Dialog>
);
