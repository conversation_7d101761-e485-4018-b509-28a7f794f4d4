import { useQuery } from "@apollo/client";
import { FileText, Loader2, Search } from "lucide-react";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  FIND_PART_BY_MPN_AND_MANUFACTURER,
  GET_PART_ALTERNATIVES,
  GetPartAlternativesQueryResult,
  PartByMpnResult,
} from "@/queries/parts";

import { useAlternativePartsTable } from "./alternative-parts-table.hook";
import { CrossGradeTooltip } from "./cross-grade-tooltip";
import { MainProductData } from "./crosses-button.types";

interface AlternativePartsTableProps {
  mpn: string;
  manufacturer: string;
  mainProduct: MainProductData;
}

export const AlternativePartsTable = ({
  mpn,
  manufacturer,
  mainProduct,
}: AlternativePartsTableProps) => {
  const { handleMpnClick, handleDatasheetClick } = useAlternativePartsTable();
  const router = useRouter();

  const handleFindSellers = (partNumber: string) => {
    router.push(`/?product=${encodeURIComponent(partNumber)}`);
  };

  const { data: partLookupData, loading: partLookupLoading } =
    useQuery<PartByMpnResult>(FIND_PART_BY_MPN_AND_MANUFACTURER, {
      variables: { keyword: mpn, limit: 10 },
    });

  const foundPart = partLookupData?.searchLocalParts?.find(
    (part) =>
      part.mpn.toLowerCase() === mpn.toLowerCase() &&
      part.manufacturer.name.toLowerCase().includes(manufacturer.toLowerCase()),
  );

  const {
    data: alternativesData,
    loading: alternativesLoading,
    error,
  } = useQuery<GetPartAlternativesQueryResult>(GET_PART_ALTERNATIVES, {
    skip: !foundPart?.id,
    variables: { partId: foundPart?.id },
  });

  const isLoading = partLookupLoading || alternativesLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading alternative parts...</span>
      </div>
    );
  }

  if (!foundPart) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-yellow-600 mb-2">Part not found in database</p>
          <p className="text-gray-500 text-sm">
            Could not find part {mpn} from {manufacturer} in our local database.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600 mb-2">Error loading alternative parts</p>
          <p className="text-gray-500 text-sm">{error.message}</p>
        </div>
      </div>
    );
  }

  const alternativeData = alternativesData?.getPartAlternatives;
  const hasAlternatives =
    alternativeData?.alternatives && alternativeData.alternatives.length > 0;

  return (
    <div className="overflow-auto max-h-[60vh]">
      <div className="mb-4 text-sm text-gray-600">
        {hasAlternatives
          ? `Found ${alternativeData?.totalCrossesFound || 0} alternative parts for part ID: ${foundPart.id}`
          : `Showing details for part ID: ${foundPart.id}`}
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>MPN</TableHead>
            <TableHead>Manufacturer</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Cross Type</TableHead>
            <TableHead>Lifecycle</TableHead>
            <TableHead>RoHS</TableHead>
            <TableHead>Package</TableHead>
            <TableHead>Datasheet</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {/* Main Product Row */}
          <TableRow className="bg-blue-50 border-l-4 border-blue-500">
            <TableCell>
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFindSellers(mainProduct.mpn)}
                        className="h-6 w-6 p-0 hover:bg-blue-100"
                      >
                        <Search className="h-3 w-3 text-blue-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Find sellers</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Button
                  variant="link"
                  className="p-0 h-auto font-mono text-blue-600 hover:text-blue-800 font-semibold"
                  onClick={() => handleMpnClick(mainProduct.mpn)}
                >
                  {mainProduct.mpn}
                </Button>
              </div>
            </TableCell>
            <TableCell className="font-medium">
              {mainProduct.manufacturer}
            </TableCell>
            <TableCell
              className="max-w-xs truncate"
              title={mainProduct.description}
            >
              {mainProduct.description}
            </TableCell>
            <TableCell>
              <span className="text-blue-700 font-medium">Original Part</span>
            </TableCell>
            <TableCell>{mainProduct.partLifecycle || "N/A"}</TableCell>
            <TableCell>{mainProduct.roHsFlag || "N/A"}</TableCell>
            <TableCell>{mainProduct.package || "N/A"}</TableCell>
            <TableCell>
              {mainProduct.dataSheet && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDatasheetClick(mainProduct.dataSheet!)}
                  className="h-8 w-8 p-0"
                >
                  <FileText className="h-4 w-4" />
                </Button>
              )}
            </TableCell>
          </TableRow>
          {hasAlternatives &&
            alternativeData?.alternatives?.map((alternative, index) => (
              <TableRow key={index}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleFindSellers(alternative.mpn)}
                            className="h-6 w-6 p-0 hover:bg-gray-100"
                          >
                            <Search className="h-3 w-3 text-gray-600" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Find sellers</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-mono text-blue-600 hover:text-blue-800"
                      onClick={() => handleMpnClick(alternative.mpn)}
                    >
                      {alternative.mpn}
                    </Button>
                  </div>
                </TableCell>
                <TableCell>{alternative.companyName}</TableCell>
                <TableCell
                  className="max-w-xs truncate"
                  title={alternative.partDescription}
                >
                  {alternative.partDescription}
                </TableCell>
                <TableCell>
                  <CrossGradeTooltip crossComment={alternative.crossComment}>
                    <span className="cursor-help border-b border-dotted border-gray-400">
                      {alternative.crossType}
                    </span>
                  </CrossGradeTooltip>
                </TableCell>
                <TableCell>{alternative.partLifecycle}</TableCell>
                <TableCell>{alternative.roHsFlag || "N/A"}</TableCell>
                <TableCell>{alternative.package}</TableCell>
                <TableCell>
                  {alternative.dataSheet && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        handleDatasheetClick(alternative.dataSheet!)
                      }
                      className="h-8 w-8 p-0"
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}

          {!hasAlternatives && (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                No alternative parts found for this MPN
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};
