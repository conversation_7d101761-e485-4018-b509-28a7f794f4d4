import { useEffect, useState } from "react";

import { Price } from "@/app/part/_components";

import { ConvertedPriceTooltip } from "./converted-price-tooltip";
import { getAllPrices } from "./product-results.util";

interface PriceListProps {
  prices: Price[];
  defaultExpanded?: boolean;
}

export const PriceList = ({
  prices,
  defaultExpanded = false,
}: PriceListProps) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  useEffect(() => {
    setIsExpanded(defaultExpanded);
  }, [defaultExpanded]);

  return (
    <div className="text-xs">
      {isExpanded ? (
        <>
          {getAllPrices(prices).map((price, i) => (
            <div key={i} className="mb-1 flex justify-between">
              <span>{price.quantity}+:</span>
              <span className="ml-2">
                <ConvertedPriceTooltip priceInfo={price} />
              </span>
            </div>
          ))}
          <button
            type="button"
            className="text-blue-600 hover:text-blue-800 text-xs cursor-pointer mb-1"
            onClick={() => setIsExpanded(false)}
          >
            Hide prices
          </button>
        </>
      ) : (
        <button
          type="button"
          className="text-blue-600 hover:text-blue-800 text-xs cursor-pointer"
          onClick={() => setIsExpanded(true)}
        >
          View all prices
        </button>
      )}
    </div>
  );
};
