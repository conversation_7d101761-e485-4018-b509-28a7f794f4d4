import {
  Too<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface AuthorizationBadgeProps {
  isAuthorized: boolean;
}

export const AuthorizationBadge = ({
  isAuthorized,
}: AuthorizationBadgeProps) => {
  const badgeColor = isAuthorized ? "text-green-500" : "text-yellow-500";
  const tooltipText = isAuthorized
    ? "This distributor is authorized"
    : "This distributor is None-authorized";

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`inline-flex items-center mr-1 ${badgeColor}`}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="m3 10.35c0 5.67 3.17 9 8.15 11.46a2 2 0 0 0 1.7 0c4.98-2.41 8.15-5.81 8.15-11.46v-4.66a2 2 0 0 0 -1.8-2 22.61 22.61 0 0 1 -6.44-1.54 1.91 1.91 0 0 0 -1.52 0 22.61 22.61 0 0 1 -6.44 1.55 2 2 0 0 0 -1.8 2z" />
              <path
                d="m11.23 14.77a1 1 0 0 1 -.7-.28l-2.28-2.24a1 1 0 0 1 0-1.42 1 1 0 0 1 1.41 0l1.58 1.55 4.11-4a1 1 0 0 1 1.41 1.43l-4.81 4.73a1 1 0 0 1 -.72.23z"
                fill="white"
              />
            </svg>
          </div>
        </TooltipTrigger>
        <TooltipContent>{tooltipText}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
