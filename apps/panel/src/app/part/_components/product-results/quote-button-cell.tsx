import { useMutation } from "@apollo/client";
import { Loader2, PenSquare } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { CREATE_QUOTE_WITH_ITEM, GET_QUOTES } from "@/queries/quotes";

import { QuoteModal } from "../QuoteModal";
import { Product } from "./product-results.type";

interface QuoteButtonCellProps {
  product: Product;
  supply: {
    sku: string;
    supplier: string;
    minOrderQuantity: number;
    stock: number;
  };
  disabled?: boolean;
  defaultQuantity?: number;
}

export const QuoteButtonCell = ({
  product,
  supply,
  disabled,
  defaultQuantity,
}: QuoteButtonCellProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreatingQuote, setIsCreatingQuote] = useState(false);

  const [createQuoteWithItem] = useMutation(CREATE_QUOTE_WITH_ITEM, {
    awaitRefetchQueries: true,
    refetchQueries: [{ query: GET_QUOTES }],
  });

  const handleCreateQuote = async (quantity: number) => {
    try {
      setIsCreatingQuote(true);
      await createQuoteWithItem({
        variables: {
          partNumber: supply.sku,
          quantity,
        },
      });
      setIsModalOpen(false);
    } catch (error) {
      console.error("Error creating quote:", error);
    } finally {
      setIsCreatingQuote(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsModalOpen(true)}
        disabled={isCreatingQuote || disabled}
      >
        {isCreatingQuote ? (
          <>
            <Loader2 className="animate-spin -ml-0.5 mr-1.5 h-3 w-3 " />
            Creating...
          </>
        ) : (
          <>
            <PenSquare className="-ml-0.5 mr-1.5 h-3 w-3 " />
            Quote
          </>
        )}
      </Button>

      <QuoteModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleCreateQuote}
        product={product}
        supply={supply}
        defaultQuantity={defaultQuantity}
      />
    </>
  );
};
