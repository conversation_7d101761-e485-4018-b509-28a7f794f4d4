"use client";

import { useSearchParams } from "next/navigation";

import { buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { LocalPart } from "@/queries/products";

import BaseProductSearch from "./BaseProductSearch";

interface ProductSearchProps {
  onSelect: (selectedProduct: string, fullPart?: LocalPart) => void;
  placeholder?: string;
  className?: string;
  variant?: "default" | "input" | "custom";
}

export default function ProductSearch({
  onSelect,
  placeholder = "Search for products (e.g. resistor, capacitor)",
  className = "",
  variant = "default",
}: ProductSearchProps) {
  const searchParams = useSearchParams();
  const initialProductValue = searchParams.get("product") || "";

  if (variant === "custom") {
    return (
      <BaseProductSearch
        onSelect={onSelect}
        className={className}
        initialValue={initialProductValue}
        renderInput={({ inputRef, value, onChange, onKeyDown, onFocus }) => (
          <div className="flex items-center pl-4 bg-white border border-gray-200 rounded-l-lg shadow-lg flex-1">
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            <input
              ref={inputRef}
              type="text"
              value={value}
              onChange={onChange}
              onKeyDown={onKeyDown}
              onFocus={onFocus}
              placeholder={placeholder}
              className="flex-1 px-4 py-4 text-gray-700 text-lg border-0 focus:outline-none focus:ring-0"
            />
          </div>
        )}
        renderSubmitButton={({ onClick }) => (
          <button
            onClick={onClick}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-medium transition-colors duration-200 rounded-r-lg border border-blue-600"
            type="button"
          >
            Go
          </button>
        )}
        renderSuggestions={({
          loading,
          suggestions,
          selectedIndex,
          onSelect,
        }) => (
          <div className="bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm mt-2 border border-gray-200">
            {loading ? (
              <div className="px-4 py-3 text-gray-500 flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500 mr-2"></div>
                <span>Loading suggestions...</span>
              </div>
            ) : suggestions.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {suggestions.map((product, index) => (
                  <li
                    key={index}
                    className={`cursor-pointer px-4 py-2 hover:bg-gray-100 ${
                      index === selectedIndex ? "bg-gray-100" : ""
                    }`}
                    onMouseDown={() => onSelect(product.mpn, product)}
                  >
                    <span className="font-medium">{product.mpn}</span>
                    {product.manufacturer?.name && (
                      <span className="ml-2 text-gray-500">
                        ({product.manufacturer.name})
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="px-4 py-2 text-gray-500">
                No suggestions found
              </div>
            )}
          </div>
        )}
      />
    );
  }

  return (
    <BaseProductSearch
      onSelect={onSelect}
      className={className}
      initialValue={initialProductValue}
      renderInput={({ inputRef, value, onChange, onKeyDown, onFocus }) =>
        variant === "input" ? (
          <Input
            ref={inputRef}
            type="text"
            value={value}
            onChange={onChange}
            onKeyDown={onKeyDown}
            onFocus={onFocus}
            placeholder={placeholder}
            className="w-full"
          />
        ) : (
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={onChange}
            onKeyDown={onKeyDown}
            onFocus={onFocus}
            placeholder={placeholder}
            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-l-md p-4"
          />
        )
      }
      renderSubmitButton={({ onClick }) =>
        variant === "default" ? (
          <button
            onClick={onClick}
            className={cn(
              buttonVariants({ variant: "default" }),
              " text-white px-4 py-2 rounded-r-md transition-colors h-[52px] rounded-bl-none rounded-tl-none",
            )}
            type="button"
          >
            Search
          </button>
        ) : null
      }
      renderSuggestions={({
        loading,
        suggestions,
        selectedIndex,
        onSelect,
      }) => (
        <div className="bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm">
          {loading ? (
            <div className="px-4 py-3 text-gray-500 flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500 mr-2"></div>
              <span>Loading suggestions...</span>
            </div>
          ) : suggestions.length > 0 ? (
            <ul className="divide-y divide-gray-200">
              {suggestions.map((product, index) => (
                <li
                  key={index}
                  className={`cursor-pointer px-4 py-2 hover:bg-gray-100 ${
                    index === selectedIndex ? "bg-gray-100" : ""
                  }`}
                  onMouseDown={() => onSelect(product.mpn, product)}
                >
                  <span className="font-medium">{product.mpn}</span>
                  {product.manufacturer?.name && (
                    <span className="ml-2 text-gray-500">
                      ({product.manufacturer.name})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <div className="px-4 py-2 text-gray-500">No suggestions found</div>
          )}
        </div>
      )}
    />
  );
}
