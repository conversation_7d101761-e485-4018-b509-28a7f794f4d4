"use client";

import { Loader2, PlusIcon } from "lucide-react";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

import { useAddToBom } from "./add-to-bom.hook";

interface AddToBomProps {
  mpn: string;
  className?: string;
  manufacturerName?: string;
}

export function AddToBom({ mpn, className, manufacturerName }: AddToBomProps) {
  const {
    selectedBomId,
    setSelectedBomId,
    quantity,
    setQuantity,
    boms,
    bomsLoading,
    isCreating,
    isSearching,
    findLocalPart,
    handleAddToBom,
    error,
  } = useAddToBom({
    manufacturerName,
    mpn,
    onSuccess: () => setOpen(false),
  });

  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (open) {
      findLocalPart();
    }
  }, [open]);

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      setQuantity(1);
      setSelectedBomId("");
    }
  };

  const isLoading = bomsLoading || isSearching || isCreating;
  const canAddToBom = selectedBomId !== "";

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Badge variant="default" className={cn("cursor-pointer", className)}>
          <PlusIcon className="h-3 w-3 mr-1" />
          Add to BOM
        </Badge>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add to Bill of Materials</DialogTitle>
          <DialogDescription>
            Add this part to an existing BOM. You can select the BOM and specify
            the quantity.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="bom" className="text-right text-sm">
              Select BOM
            </label>
            <div className="col-span-3">
              <Select
                disabled={isLoading || boms.length === 0}
                value={selectedBomId}
                onValueChange={setSelectedBomId}
              >
                <SelectTrigger id="bom">
                  <SelectValue placeholder="Select a BOM" />
                </SelectTrigger>
                <SelectContent>
                  {boms.map((bom) => (
                    <SelectItem key={bom.id} value={String(bom.id)}>
                      {bom.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="quantity" className="text-right text-sm">
              Quantity
            </label>
            <Input
              id="quantity"
              type="number"
              min="1"
              className="col-span-3"
              value={quantity}
              onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
              disabled={isLoading}
            />
          </div>
          {error && (
            <div className="text-yellow-800 text-sm p-2 bg-yellow-50 rounded">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button onClick={handleAddToBom} disabled={isLoading || !canAddToBom}>
            {isCreating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding...
              </>
            ) : (
              "Add to BOM"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
