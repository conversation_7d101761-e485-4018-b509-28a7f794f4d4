import {
  useApolloClient,
  useLazyQuery,
  useMutation,
  useQuery,
} from "@apollo/client";
import { useState } from "react";

import { CREATE_BOM_ITEM } from "@/queries/bom_items";
import { BomsQueryResult, GET_BOMS } from "@/queries/boms";
import {
  LocalPart,
  SEARCH_LOCAL_PARTS,
  SearchLocalPartsResponse,
} from "@/queries/products";
import { CreateBomItemMutationResult } from "@/queries/types";

interface UseAddToBomProps {
  mpn: string;
  onSuccess?: () => void;
  manufacturerName?: string;
}

export const useAddToBom = ({
  mpn,
  onSuccess,
  manufacturerName,
}: UseAddToBomProps) => {
  const client = useApolloClient();
  const [selectedBomId, setSelectedBomId] = useState<string>("");
  const [quantity, setQuantity] = useState<number>(1);
  const [error, setError] = useState<string | null>(null);

  // Fetch all BOMs
  const { data: bomsData, loading: bomsLoading } = useQuery<BomsQueryResult>(
    GET_BOMS,
    {
      fetchPolicy: "cache-and-network",
    },
  );

  // Search for the part by MPN
  const [searchLocalParts, { loading: isSearching }] =
    useLazyQuery<SearchLocalPartsResponse>(SEARCH_LOCAL_PARTS, {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        const localParts = data?.searchLocalParts || [];

        // Find exact match by MPN
        const exactMatch = localParts.find(
          (part: LocalPart) => part.mpn.toLowerCase() === mpn.toLowerCase(),
        );

        if (exactMatch) {
          setError(null);
        } else {
          setError("No exact match found for this part in our database.");
        }
      },
      onError: (error) => {
        setError("Failed to search for part in database.");
      },
    });

  // Create a BOM item mutation
  const [createBomItem, { loading: isCreating }] = useMutation<
    CreateBomItemMutationResult,
    {
      createBomItemInput: {
        bom_id: number;
        mpn: string;
        manufacturer_name: string;
        quantity: number;
      };
    }
  >(CREATE_BOM_ITEM, {
    onCompleted: () => {
      if (selectedBomId) {
        client.cache.modify({
          fields: {
            bom_items: () => undefined,
          },
          id: `Bom:${selectedBomId}`,
        });
      }

      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const findLocalPart = () => {
    setError(null);
    searchLocalParts({ variables: { keyword: mpn, limit: 10 } });
  };

  const handleAddToBom = async () => {
    if (!selectedBomId) {
      setError("Please select a BOM.");
      return;
    }

    try {
      // Get the part details from the search results
      const localParts = await searchLocalParts({
        variables: { keyword: mpn, limit: 10 },
      });

      const exactMatch = localParts.data?.searchLocalParts.find(
        (part: LocalPart) => part.mpn.toLowerCase() === mpn.toLowerCase(),
      );

      const createBomItemInput = {
        bom_id: parseInt(selectedBomId, 10),
        manufacturer_name:
          exactMatch?.manufacturer?.name || manufacturerName || "Unknown",
        mpn,
        quantity,
      };

      // Do not send matched_part_id or matched_status; backend will handle matching

      await createBomItem({
        variables: {
          createBomItemInput,
        },
      });
    } catch (err) {
      console.error("Error adding item to BOM:", err);
      setError("Error adding item to BOM: " + (err as Error).message);
    }
  };

  return {
    boms: bomsData?.boms || [],
    bomsLoading,
    error,
    findLocalPart,
    handleAddToBom,
    isCreating,
    isSearching,
    quantity,
    selectedBomId,
    setQuantity,
    setSelectedBomId,
  };
};
