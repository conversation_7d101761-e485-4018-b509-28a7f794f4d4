"use client";

import { useLazyQuery } from "@apollo/client";
import { useQueryState } from "nuqs";
import { useEffect, useState } from "react";

import { RawDataDialog } from "@/components/feature";
import {
  SEARCH_PRODUCTS_GROUPED_BY_MANUFACTURER,
  SearchProductGroupedResult,
} from "@/queries/products";

import { ProductResultsGrouped } from "./_components";
import {
  FilterPanel,
  FilterState,
} from "./_components/filter-panel/filter-panel";
import { applyFiltersToProduct } from "./_components/filter-panel/filter-utils";
import ProductSearch from "./_components/ProductSearch";

export const PageContainer = () => {
  const [selectedProduct, setSelectedProduct] = useQueryState("product");
  const [searchProducts, { loading, data }] =
    useLazyQuery<SearchProductGroupedResult>(
      SEARCH_PRODUCTS_GROUPED_BY_MANUFACTURER,
    );
  const [activeFilters, setActiveFilters] = useState<FilterState>({
    authorizationStatus: new Set<string>(),
    leadTimes: new Set<string>(),
    manufacturers: new Set<string>(),
    packagings: new Set<string>(),
    stockRange: new Set<string>(),
    suppliers: new Set<string>(),
  });

  useEffect(() => {
    if (selectedProduct) {
      searchProducts({ variables: { keyword: selectedProduct, limit: 5 } });
    }
  }, [selectedProduct, searchProducts]);

  const handleProductSearch = async (product: string) => {
    setSelectedProduct(product);
  };

  const handleFilterChange = (filters: FilterState) => {
    setActiveFilters(filters);
  };

  const isProductWithSupplier = (group: { supplies: unknown[] }) =>
    group.supplies.length > 0;
  const doesProductHaveManufacturerGroup = (product: {
    manufacturerGroups: { supplies: unknown[] }[];
  }) => product.manufacturerGroups.length > 0;

  // Apply filters to search results - need to adapt for grouped structure
  const filteredProducts = data?.searchProductsGroupedByManufacturer
    ?.searchProducts
    ? data.searchProductsGroupedByManufacturer.searchProducts
        .map((product) => {
          // For each product, filter manufacturer groups
          const filteredGroups = product.manufacturerGroups
            .map((group) => {
              // Create a temporary product structure for filtering
              const tempProduct = {
                description: product.description,
                leadTime: product.leadTime,
                mpn: product.mpn,
                supplies: group.supplies,
              };
              const filteredTempProduct = applyFiltersToProduct(
                tempProduct,
                activeFilters,
              );
              return {
                ...group,
                supplies: filteredTempProduct.supplies,
              };
            })
            .filter(isProductWithSupplier);

          return {
            ...product,
            manufacturerGroups: filteredGroups,
          };
        })
        .filter(doesProductHaveManufacturerGroup)
    : [];

  // Check if any products have supplies after filtering
  const hasFilteredResults = filteredProducts.some((product) =>
    product.manufacturerGroups.some((group) => group.supplies.length > 0),
  );

  return (
    <div>
      <div className="text-center">
        <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
          Electronic Component Search
        </h1>
        <p className="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
          Search for electronic components across multiple suppliers
        </p>
      </div>

      <div className="mt-10 sm:mt-12">
        <ProductSearch
          onSelect={handleProductSearch}
          className="mt-3"
          placeholder="Type a part number or search term"
        />
        <p className="mt-2 text-sm text-gray-500">
          Select from suggestions or enter your own search term and press Enter
          or Search
        </p>
      </div>

      {loading && (
        <div className="mt-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
          <p className="mt-2 text-gray-600">Searching for components...</p>
        </div>
      )}

      {data?.searchProductsGroupedByManufacturer?.searchProducts &&
      data.searchProductsGroupedByManufacturer.searchProducts.length > 0 ? (
        <div className="mt-8">
          <FilterPanel
            supplies={data.searchProductsGroupedByManufacturer.searchProducts.flatMap(
              (p) => p.manufacturerGroups.flatMap((g) => g.supplies),
            )}
            onFilterChange={handleFilterChange}
          />
          <div className="mb-4 flex justify-between items-center">
            <RawDataDialog
              rawData={data.searchProductsGroupedByManufacturer.rawData}
              withTrigger
            />
          </div>
          {hasFilteredResults ? (
            filteredProducts.map(
              (product, index) =>
                product.manufacturerGroups.length > 0 && (
                  <ProductResultsGrouped
                    key={`${product.mpn}-${index}`}
                    product={product}
                    showQuoteButton={false}
                  />
                ),
            )
          ) : (
            <div className="mt-8 text-center text-gray-500">
              No results match the selected filters
            </div>
          )}
        </div>
      ) : (
        data?.searchProductsGroupedByManufacturer?.searchProducts &&
        !loading && (
          <div className="mt-8 text-center text-gray-500">No results found</div>
        )
      )}
    </div>
  );
};
