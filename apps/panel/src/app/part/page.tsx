import { Suspense } from "react";

import { Skeleton } from "@/components/ui/skeleton";

import { PageContainer } from "./page-container";

function PartPageSkeleton() {
  return (
    <div>
      <div className="text-center">
        <Skeleton className="h-10 w-96 mx-auto mb-4" />
        <Skeleton className="h-6 w-80 mx-auto" />
      </div>

      <div className="mt-10 sm:mt-12">
        <Skeleton className="h-12 w-full max-w-md mx-auto" />
        <Skeleton className="h-4 w-64 mx-auto mt-2" />
      </div>

      <div className="mt-8 space-y-4">
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-40 w-full" />
      </div>
    </div>
  );
}

export default function Page() {
  return (
    <Suspense fallback={<PartPageSkeleton />}>
      <PageContainer />
    </Suspense>
  );
}
