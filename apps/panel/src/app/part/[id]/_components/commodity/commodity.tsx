import React, { <PERSON> } from "react";
import { v4 as uuidv4 } from "uuid";

import { CommodityChartContainer } from "@/components/feature";

import { CommodityProps } from "./commodity.type";

const Commodity: FC<CommodityProps> = ({
  commodity,
  subCommodity,
  subSubCommodity,
}) => {
  const defaultFilterSet = {
    id: uuidv4(),
    manufacturerId: undefined,
    gradeId: undefined,
    packagingId: undefined,
  };

  // Determine the most specific commodity to display
  const displayCommodity =
    subSubCommodity?.name || subCommodity?.name || commodity?.name;

  return (
    <div>
      <div className="mb-4 mt-4">
        <p className="text-lg text-gray-600">
          Commodity: <span className="font-medium">{displayCommodity}</span>
        </p>
      </div>
      <CommodityChartContainer
        filterSets={[defaultFilterSet]}
        baseFilters={{
          commodityId: commodity?.id,
          subCommodityId: subCommodity?.id,
          subSubCommodityId: subSubCommodity?.id,
        }}
        enabled={true}
      />
    </div>
  );
};

export default Commodity;
