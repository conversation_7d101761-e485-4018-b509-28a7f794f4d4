import { FC } from "react";

import { ChartContainer } from "@/components/feature";

import { TrendProps } from "./trend.type";

const Trend: FC<TrendProps> = ({ partId, loading }) => {
  if (loading) return <div>Loading...</div>;
  if (!partId) return <div>Part not found</div>;

  return (
    <div>
      <ChartContainer
        partId={partId.toString()}
        enabled={true}
        priceBreak="1000"
        breakMethod="avg"
        disableAI
      />
    </div>
  );
};

export default Trend;
