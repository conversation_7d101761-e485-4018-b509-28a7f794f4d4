import { ChartDataState as ComponentChartDataState } from "@/components/primitives/chart/chart.type";

export const transformChartData = (
  data: Record<string, { week: string; value: number | null }[]> | null,
): ComponentChartDataState => {
  if (!data) return null;

  return Object.entries(data).reduce(
    (acc, [key, items]) => {
      acc[key] = {
        items: items.map((item) => ({ value: item.value, week: item.week })),
        link: "",
      };
      return acc;
    },
    {} as Record<
      string,
      { items: { week: string; value: number | null }[]; link: string }
    >,
  );
};
