import React, { FC } from "react";

import { ProductResultsGrouped } from "@/app/part/_components";
import { AlternativePartsTable } from "@/app/part/_components/product-results/crosses-button";

import { usePartDetail } from "./part-detail.hook";
import { PartDetailProps } from "./part-detail.type";

export const PartDetail: FC<PartDetailProps> = ({ mpn }) => {
  const { loading, product, manufacturer, mainProduct } = usePartDetail({
    mpn,
  });

  return (
    <>
      <h1 className="text-2xl font-bold mb-6 mt-6">Market Data: {mpn}</h1>
      {loading && <div>Loading...</div>}
      {!loading && product && (
        <>
          <ProductResultsGrouped
            product={product}
            disableCrossesButton
            disableTrendButton
          />
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Alternative Parts</h2>
            {mainProduct && (
              <AlternativePartsTable
                mpn={mpn}
                manufacturer={manufacturer}
                mainProduct={mainProduct}
              />
            )}
          </div>
        </>
      )}
      {!loading && !product && (
        <div className="text-gray-500">No product found for this MPN.</div>
      )}
    </>
  );
};
