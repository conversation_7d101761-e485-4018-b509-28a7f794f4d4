import { useLazyQuery } from "@apollo/client";
import { useEffect, useState } from "react";

import { MainProductData } from "@/app/part/_components/product-results/crosses-button/crosses-button.types";
import {
  ProductGrouped,
  SEARCH_PRODUCTS_GROUPED_BY_MANUFACTURER,
  SearchProductGroupedResult,
} from "@/queries/products";

import { PartDetailProps } from "./part-detail.type";

export const usePartDetail = ({ mpn }: PartDetailProps) => {
  const [product, setProduct] = useState<ProductGrouped | null>(null);
  const [manufacturer, setManufacturer] = useState<string>("");
  const [mainProduct, setMainProduct] = useState<MainProductData | null>(null);

  const [searchProducts, { loading, data }] =
    useLazyQuery<SearchProductGroupedResult>(
      SEARCH_PRODUCTS_GROUPED_BY_MANUFACTURER,
    );

  useEffect(() => {
    if (mpn) {
      searchProducts({ variables: { keyword: mpn, limit: 1 } });
    }
  }, [mpn, searchProducts]);

  useEffect(() => {
    if (data?.searchProductsGroupedByManufacturer?.searchProducts?.length) {
      const prod = data.searchProductsGroupedByManufacturer.searchProducts[0];
      setProduct(prod);
      // Use the first manufacturer group for details
      const group = prod.manufacturerGroups[0];
      setManufacturer(group?.manufacturer || "");
      setMainProduct({
        mpn: prod.mpn,
        manufacturer: group?.manufacturer || "",
        description: prod.description,
        dataSheet: group?.supplies.find((s) => s.supplierDatasheetUrl)
          ?.supplierDatasheetUrl,
        package: undefined,
        partLifecycle: undefined,
        roHsFlag: undefined,
      });
    }
  }, [data]);

  return { mpn, loading, product, manufacturer, mainProduct };
};
