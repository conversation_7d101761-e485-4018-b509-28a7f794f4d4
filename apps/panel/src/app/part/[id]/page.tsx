"use client";

import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { useQueryState } from "nuqs";

import { Commodity, PartDetail, Trend } from "./_components";
import { usePartPage } from "./page.hook";

function ChartSkeleton() {
  return (
    <div className="bg-white rounded-lg border p-6 animate-pulse">
      <div className="mb-4">
        <div className="h-5 bg-gray-200 rounded w-32 mb-2"></div>
        <div className="h-4 bg-gray-100 rounded w-24"></div>
      </div>
      <div className="bg-gray-100 rounded" style={{ height: "460px" }}></div>
    </div>
  );
}

function LoadingSkeleton({ mpn }: { mpn: string }) {
  return (
    <div className="max-w-[1440px] mx-auto px-4">
      <div className="flex flex-col space-y-6 mb-8">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Link
            href="/"
            className="hover:text-blue-600 transition-colors flex items-center group"
          >
            <ArrowLeftIcon className="mr-1 h-4 w-4 transition-transform group-hover:-translate-x-1" />
            <span>Product Search</span>
          </Link>
          <span>/</span>
          <span className="text-gray-900 font-medium truncate max-w-[300px]">
            {mpn}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <ChartSkeleton />
        <ChartSkeleton />
        <ChartSkeleton />
        <ChartSkeleton />
      </div>
    </div>
  );
}

export default function PartDetailPage() {
  const { mpn, part, loading } = usePartPage();
  const [searchTerm] = useQueryState("search-term");

  if (loading) {
    return <LoadingSkeleton mpn={mpn} />;
  }

  return (
    <div className="max-w-[1440px] mx-auto  px-4">
      <div className="flex flex-col space-y-6 mb-8">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Link
            href={`/part${searchTerm ? "?product=" + searchTerm : ""}`}
            className="hover:text-blue-600 transition-colors flex items-center group"
          >
            <ArrowLeftIcon className="mr-1 h-4 w-4 transition-transform group-hover:-translate-x-1" />
            <span>Product Search</span>
          </Link>
          <span>/</span>
          <span className="text-gray-900 font-medium truncate max-w-[300px]">
            {mpn}
          </span>
        </div>
      </div>

      <Trend partId={part?.id} loading={loading} />
      <PartDetail mpn={mpn} />
      <Commodity
        commodity={{
          id: part?.commodityId?.toString() ?? "",
          name: part?.commodity ?? "",
        }}
        subCommodity={{
          id: part?.subCommodityId?.toString() ?? "",
          name: part?.subCommodity ?? "",
        }}
        subSubCommodity={{
          id: part?.subSubCommodityId?.toString() ?? "",
          name: part?.subSubCommodity ?? "",
        }}
      />
    </div>
  );
}
