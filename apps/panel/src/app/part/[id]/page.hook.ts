import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

import { fetchPart } from "@/lib/mi/apis";
import { PartWithCommodityHierarchy } from "@/lib/mi/react-query/parts";

export const usePartPage = () => {
  const [part, setPart] = useState<
    PartWithCommodityHierarchy | null | undefined
  >(undefined);
  const [loading, setLoading] = useState(true);
  const params = useParams();
  const mpn = decodeURIComponent(params.id as string);

  useEffect(() => {
    async function fetcher() {
      setLoading(true);
      const part = await fetchPart({ query: mpn });
      setLoading(false);
      if (part) {
        setPart(part);
      } else {
        setPart(null);
      }
    }
    fetcher();
  }, [mpn]);
  return { mpn, part, loading };
};
