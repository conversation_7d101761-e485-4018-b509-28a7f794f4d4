"use client";

import { HttpLink } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import {
  ApolloClient,
  ApolloNextAppProvider,
  InMemoryCache,
} from "@apollo/experimental-nextjs-app-support";

import { getAuthHeaders } from "./auth-oidc.utils";

// Create a makeClient function that can be used in the ApolloNextAppProvider
function makeClient() {
  // Create the HTTP link for GraphQL endpoint
  const httpLink = new HttpLink({
    uri: process.env.NEXT_PUBLIC_API_BASE + "/graphql",
  });

  // Create the auth link that adds authentication headers
  const authLink = setContext(async (_, { headers }) => {
    try {
      // Get authentication headers (includes Bearer token if user is authenticated)
      const authHeaders = await getAuthHeaders();

      // Log authentication status for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        const hasToken = authHeaders.Authorization ? 'with auth token' : 'without auth token';
        console.log(`[Apollo Auth] Making GraphQL request ${hasToken}`);
      }

      return {
        headers: {
          ...headers,
          ...authHeaders,
        },
      };
    } catch (error) {
      // If auth fails, continue without auth headers
      console.warn("Failed to get auth headers:", error);
      return {
        headers: {
          ...headers,
        },
      };
    }
  });

  return new ApolloClient({
    cache: new InMemoryCache(),
    // Chain the auth link with the HTTP link
    link: authLink.concat(httpLink),
  });
}

// Export the ApolloWrapper component to use in layout.tsx
export function ApolloWrapper({ children }: { children: React.ReactNode }) {
  return (
    <ApolloNextAppProvider makeClient={makeClient}>
      {children}
    </ApolloNextAppProvider>
  );
}
